import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { BsModalService } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';

@Component({
  selector: 'integration-card',
  templateUrl: './integration-card.component.html'
})
export class IntegrationCardComponent implements OnInit {
  @Input() isSkeleton: boolean = false;
  @Input() integration: {
    displayName: string;
    name: string;
    image: string;
    logo: string;
    description: string;
    value: number;
    id: string;
    isLRSource?: boolean;
  };
  @Input() functionType: string;
  @Output() connectNowClicked: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private modalService: BsModalService,
    private router: Router,
  ) { }

  ngOnInit() {
    console.log(this.integration, 'integration');
  }


  // openIntegration(image: string, displayName: string, name: string) {  
  //   if (this.functionType) {
  //     this.connectNowClicked?.emit();
  //     return;
  //   }

  //   this.router.navigate([`/global-config/${displayName}`], {
  //     state: {
  //       image: image,
  //       displayName: displayName,
  //       name: name
  //     }
  //   });

  // }
  openIntegration(image: string, displayName: string, name: string) {
    if (this.functionType) {
      this.connectNowClicked?.emit();
      return;
    }

    const integrationData = {
      image,
      displayName,
      name: this.integration.value,
      originalName: name,
      value: this.integration.value,
      id: this.integration.id,
      isLRSource: this.integration.isLRSource
    };
    localStorage.setItem('integrationData', JSON.stringify(integrationData));
    this.router.navigate([`/global-config/integration`]);
  }

  // openCommonFloorIntegration(
  //   image: string,
  //   displayName: string,
  //   name: string,
  //   count: string
  // ) {  
  //   if (this.functionType) {
  //     this.connectNowClicked?.emit();
  //     return;
  //   }

  //   localStorage.setItem('integrationData', JSON.stringify({ image, displayName, name, count }));

  //   this.router.navigate([`/global-config/integrations`]);
  // }

}
