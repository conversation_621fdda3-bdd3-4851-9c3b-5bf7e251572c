<!-- Header -->
<div class="bg-dark w-100 px-16 py-12 text-white flex-between">
  <div class="align-center">
    <div class="icon ic-envelope-solid ic-large mr-8"></div>
    <h4 class="fw-semi-bold">API Email Integration</h4>
  </div>
  <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
</div>
<!-- Tabs -->
<div class="align-center gap-4 px-20 pt-16">
  <div class="align-center border br-30 p-4 bg-white">
    <!-- Push Tab -->
    <div class="align-center cursor-pointer px-24 py-10 br-20"
      [ngClass]="activeTab === 'push' ? 'bg-coal text-white' : 'text-gray'" (click)="switchTab('push')">
      <div class="bg-gray-dark mr-6 br-4">
        <div class="icon ic-upload ic-xs" [ngClass]="activeTab === 'push' ? 'text-black' : 'text-white'"></div>
      </div>
      <div class="flex-col">
        <span class="fw-600 text-xs">Push API</span>
        <span class="text-xxs" [ngClass]="activeTab === 'push' && 'text-accent-green'">we receive data</span>
      </div>
    </div>
    <!-- Pull Tab -->
    <div class="align-center cursor-pointer px-24 py-10 br-20 ml-4"
      [ngClass]="activeTab === 'pull' ? 'bg-coal text-white' : 'text-gray'" (click)="switchTab('pull')">
      <div class="bg-gray-dark mr-6 br-4">
        <div class="icon ic-download ic-xs" [ngClass]="activeTab === 'pull' ? 'text-black' : 'text-white'"></div>
      </div>
      <div class="flex-col">
        <span class="fw-600 text-xs">Pull API</span>
        <span class="text-xxs" [ngClass]="activeTab === 'pull' && 'text-accent-green'">we fetch data</span>
      </div>
    </div>
  </div>
</div>
<!-- Form Content -->
<div class="px-16 ip-min-w-350 ip-max-w-350 h-100-176 scrollbar">
  <!-- Push Form -->
  <form [formGroup]="pushForm" *ngIf="activeTab === 'push'">
    <!-- Account Name and Login Email Row -->
    <div class="d-flex">
      <div class="flex-grow-1">
        <div class="field-label-req">Account Name</div>
        <form-errors-wrapper label="Account Name" [control]="pushForm.controls['accountName']">
          <input type="text" required formControlName="accountName" autocomplete="off" placeholder="ex. Abid" />
        </form-errors-wrapper>
      </div>
      <div class="flex-grow-1 ml-16">
        <div class="field-label">Login Id/Login Email</div>
        <form-errors-wrapper label="Login Id/Login Email" [control]="pushForm.controls['loginEmail']">
          <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>
      </div>
    </div>
    <!-- Relationship Manager Email -->
    <div class="field-label-req">Relationship Manager Email</div>
    <form-errors-wrapper label="Relationship Manager Email" [control]="pushForm.controls['relationshipManagerEmail']">
      <div class="flex-between position-relative">
        <input type="email" required placeholder="ex. <EMAIL>" #pushEmailToInput class="outline-0 padd-r pr-36"
          autocomplete="off" (keydown)="onEmailKeyPress($event, pushRelationshipManagerEmail, pushEmailToInput)"
          [ngClass]="(pushEmailToInput.value && !validateEmail(pushEmailToInput.value)) ? 'border-red-800': ''" />
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
          (click)="addEmail(pushRelationshipManagerEmail, pushEmailToInput.value, pushEmailToInput)">
          <span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
      <div *ngIf="pushEmailToInput.value && !validateEmail(pushEmailToInput.value)"
        class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
        Enter a valid Email ID.
      </div>
    </form-errors-wrapper>
    <!-- Email Tags Display -->
    <div class="d-flex flex-wrap mt-12" *ngIf="getValidEmails(pushRelationshipManagerEmail).length > 0">
      <ng-container *ngFor="let email of pushRelationshipManagerEmail.controls; let i = index">
        <div *ngIf="email.value && email.value.trim()"
          class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
          <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer">{{ email.value }}</span>
          <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
            (click)="removeEmail(i, pushRelationshipManagerEmail)"></span>
        </div>
      </ng-container>
    </div>
    <!-- Additional Email -->
    <div class="field-label">Additional Email</div>
    <form-errors-wrapper label="Additional Email" [control]="pushForm.controls['additionalEmail']">
      <div class="flex-between position-relative">
        <input type="email" placeholder="ex. <EMAIL>" #pushEmailCcInput class="outline-0 padd-r pr-36"
          autocomplete="off" (keydown)="onEmailKeyPress($event, pushAdditionalEmail, pushEmailCcInput)"
          [ngClass]="pushEmailCcInput.value && !validateEmail(pushEmailCcInput.value) ? 'border-red-800': ''" />
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
          (click)="addEmail(pushAdditionalEmail, pushEmailCcInput.value, pushEmailCcInput)">
          <span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
      <div *ngIf="pushEmailCcInput.value && !validateEmail(pushEmailCcInput.value)"
        class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
        Enter a valid Email ID.
      </div>
    </form-errors-wrapper>
    <div class="d-flex flex-wrap mt-12" *ngIf="getValidEmails(pushAdditionalEmail).length > 0">
      <ng-container *ngFor="let email of pushAdditionalEmail.controls; let i = index">
        <div *ngIf="email.value && email.value.trim()"
          class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
          <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer">{{ email.value }}</span>
          <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
            (click)="removeEmail(i, pushAdditionalEmail)"></span>
        </div>
      </ng-container>
    </div>
    <!-- BCC Email -->
    <div class="field-label">BCC Email</div>
    <form-errors-wrapper label="BCC Email" [control]="pushForm.controls['bccEmail']">
      <div class="flex-between position-relative">
        <input type="email" placeholder="ex. <EMAIL>" #pushEmailBccInput class="outline-0 padd-r pr-36"
          autocomplete="off" (keydown)="onEmailKeyPress($event, pushBccEmail, pushEmailBccInput)"
          [ngClass]="pushEmailBccInput.value && !validateEmail(pushEmailBccInput.value) ? 'border-red-800': ''" />
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
          (click)="addEmail(pushBccEmail, pushEmailBccInput.value, pushEmailBccInput)">
          <span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
      <div *ngIf="pushEmailBccInput.value && !validateEmail(pushEmailBccInput.value)"
        class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
        Enter a valid Email ID.
      </div>
    </form-errors-wrapper>
    <div class="d-flex flex-wrap mt-12" *ngIf="getValidEmails(pushBccEmail).length > 0">
      <ng-container *ngFor="let email of pushBccEmail.controls; let i = index">
        <div *ngIf="email.value && email.value.trim()"
          class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
          <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer">{{ email.value }}</span>
          <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
            (click)="removeEmail(i, pushBccEmail)"></span>
        </div>
      </ng-container>
    </div>
    <!-- cURL/Payload -->
    <div class="d-flex align-items-end" style="gap: 14px;">
      <div class="flex-grow-1">
        <div class="field-label">cURL/Payload</div>
        <form-errors-wrapper label="cURL/Payload" [control]="pushForm.controls['curlPayload']">
          <textarea formControlName="curlPayload" placeholder="enter curl/payload" rows="3" class="w-100"
            (input)="onPayloadChange()"></textarea>
        </form-errors-wrapper>
      </div>
      <button type="button" class="bg-accent-green w-120 h-40 text-white h5 border-0 br-4" (click)="extractFields()"
        [disabled]="isExtractingFields">
        <span *ngIf="!isExtractingFields">Extract Fields</span>
        <span *ngIf="isExtractingFields">Extracting...</span>
      </button>
    </div>
    <!-- Fields Status -->
    <div *ngIf="extractedFields && extractedFields.length > 0" class="mt-8 text-xs text-accent-green">
      ✓ {{extractedFields.length}} fields available for mapping
    </div>
    <!-- Payload Mapping -->
    <div class="mt-16">
      <div class="field-label-req">Payload Mapping</div>
      <div formArrayName="payloadMapping">
        <div *ngFor="let mapping of pushPayloadMapping.controls; let i = index" [formGroupName]="i">
          <div class="d-flex align-items-center mb-16" style="gap: 14px;">
            <div style="flex: 1; width: 50%;">
              <form-errors-wrapper label="Field Name" [control]="mapping.get('fieldName')">
                <ng-select formControlName="fieldName" [items]="formFields" placeholder="Select field name"
                  [clearable]="true" [trackByFn]="trackByFn" class="bg-white">
                </ng-select>
              </form-errors-wrapper>
            </div>
            <div style="flex: 1; width: 50%;">
              <form-errors-wrapper label="Mapped Value" [control]="mapping.get('mappedValue')">
                <!-- Show dropdown when fields are extracted -->
                <ng-select *ngIf="extractedFields && extractedFields.length > 0" formControlName="mappedValue"
                  [items]="extractedFields" placeholder="Select mapped value" [clearable]="true" class="bg-white">
                </ng-select>
                <!-- Show input when no fields are extracted -->
                <input *ngIf="!extractedFields || extractedFields.length === 0" type="text"
                  formControlName="mappedValue" placeholder="Extract fields or enter manually" />
              </form-errors-wrapper>
            </div>
            <!-- Hide delete button for first 2 fixed fields -->
            <div *ngIf="i >= 2" class="bg-light-red cursor-pointer"
              style="width: 29px; height: 29px; border-radius: 6px; display: flex; align-items: center; justify-content: center;"
              (click)="removePayloadMapping(i)">
              <span class="icon ic-delete ic-xxxs"></span>
            </div>
            <!-- Show placeholder for first 2 fields to maintain alignment -->
            <div *ngIf="i < 2" style="width: 29px; height: 29px;"></div>
          </div>
        </div>
      </div>
      <div class="cursor-pointer align-center fw-700 mt-12">
        <span class="icon ic-xs ic-add ic-accent-green"></span>
        <span class="text-accent-green ml-4" (click)="addPayloadMapping()">Add Additional payload</span>
      </div>
    </div>
  </form>
  <!-- Pull Form -->
  <form [formGroup]="pullForm" *ngIf="activeTab === 'pull'">
    <!-- Webhook URL -->
    <div class="mb-16">
      <div class="field-label">Webhook URL</div>
      <form-errors-wrapper label="Webhook URL" [control]="pullForm.controls['webhookUrl']">
        <input type="url" formControlName="webhookUrl" placeholder="https://example.com/webhook" />
      </form-errors-wrapper>
    </div>
    <!-- Account Name and Login Email Row -->
    <div class="d-flex mb-16" style="gap: 16px;">
      <div class="flex-grow-1">
        <div class="field-label-req">Account Name</div>
        <form-errors-wrapper label="Account Name" [control]="pullForm.controls['accountName']">
          <input type="text" required formControlName="accountName" autocomplete="off" placeholder="ex. Abid" />
        </form-errors-wrapper>
      </div>
      <div class="flex-grow-1">
        <div class="field-label">Login Id/Login Email</div>
        <form-errors-wrapper label="Login Id/Login Email" [control]="pullForm.controls['loginEmail']">
          <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
        </form-errors-wrapper>
      </div>
    </div>
    <!-- Relationship Manager Email for Pull -->
    <div class="field-label-req">Relationship Manager Email</div>
    <form-errors-wrapper label="Relationship Manager Email" [control]="pullForm.controls['relationshipManagerEmail']">
      <div class="flex-between position-relative">
        <input type="email" required placeholder="ex. <EMAIL>" #pullEmailToInput class="outline-0 padd-r pr-36"
          autocomplete="off" (keydown)="onEmailKeyPress($event, pullRelationshipManagerEmail, pullEmailToInput)"
          [ngClass]="(pullEmailToInput.value && !validateEmail(pullEmailToInput.value)) ? 'border-red-800': ''" />
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
          (click)="addEmail(pullRelationshipManagerEmail, pullEmailToInput.value, pullEmailToInput)">
          <span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
      <div *ngIf="pullEmailToInput.value && !validateEmail(pullEmailToInput.value)"
        class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
        Enter a valid Email ID.
      </div>
    </form-errors-wrapper>
    <div class="d-flex flex-wrap mt-12" *ngIf="getValidEmails(pullRelationshipManagerEmail).length > 0">
      <ng-container *ngFor="let email of pullRelationshipManagerEmail.controls; let i = index">
        <div *ngIf="email.value && email.value.trim()"
          class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
          <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer">{{ email.value }}</span>
          <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
            (click)="removeEmail(i, pullRelationshipManagerEmail)"></span>
        </div>
      </ng-container>
    </div>
    <!-- Additional Email for Pull -->
    <div class="field-label">Additional Email</div>
    <form-errors-wrapper label="Additional Email" [control]="pullForm.controls['additionalEmail']">
      <div class="flex-between position-relative">
        <input type="email" placeholder="ex. <EMAIL>" #pullEmailCcInput class="outline-0 padd-r pr-36"
          autocomplete="off" (keydown)="onEmailKeyPress($event, pullAdditionalEmail, pullEmailCcInput)"
          [ngClass]="pullEmailCcInput.value && !validateEmail(pullEmailCcInput.value) ? 'border-red-800': ''" />
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
          (click)="addEmail(pullAdditionalEmail, pullEmailCcInput.value, pullEmailCcInput)">
          <span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
      <div *ngIf="pullEmailCcInput.value && !validateEmail(pullEmailCcInput.value)"
        class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
        Enter a valid Email ID.
      </div>
    </form-errors-wrapper>
    <div class="d-flex flex-wrap mt-12" *ngIf="getValidEmails(pullAdditionalEmail).length > 0">
      <ng-container *ngFor="let email of pullAdditionalEmail.controls; let i = index">
        <div *ngIf="email.value && email.value.trim()"
          class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
          <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer">{{ email.value }}</span>
          <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
            (click)="removeEmail(i, pullAdditionalEmail)"></span>
        </div>
      </ng-container>
    </div>
    <!-- BCC Email for Pull -->
    <div class="field-label">BCC Email</div>
    <form-errors-wrapper label="BCC Email" [control]="pullForm.controls['bccEmail']">
      <div class="flex-between position-relative">
        <input type="email" placeholder="ex. <EMAIL>" #pullEmailBccInput class="outline-0 padd-r pr-36"
          autocomplete="off" (keydown)="onEmailKeyPress($event, pullBccEmail, pullEmailBccInput)"
          [ngClass]="pullEmailBccInput.value && !validateEmail(pullEmailBccInput.value) ? 'border-red-800': ''" />
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
          (click)="addEmail(pullBccEmail, pullEmailBccInput.value, pullEmailBccInput)">
          <span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
      <div *ngIf="pullEmailBccInput.value && !validateEmail(pullEmailBccInput.value)"
        class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
        Enter a valid Email ID.
      </div>
    </form-errors-wrapper>
    <div class="d-flex flex-wrap mt-12" *ngIf="getValidEmails(pullBccEmail).length > 0">
      <ng-container *ngFor="let email of pullBccEmail.controls; let i = index">
        <div *ngIf="email.value && email.value.trim()"
          class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
          <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer">{{ email.value }}</span>
          <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
            (click)="removeEmail(i, pullBccEmail)"></span>
        </div>
      </ng-container>
    </div>
    <!-- Method Type and Content Type Row -->
    <div class="d-flex mb-16" style="gap: 16px;">
      <div class="flex-grow-1">
        <div class="field-label-req">Method Type</div>
        <form-errors-wrapper label="Method Type" [control]="pullForm.controls['methodType']">
          <ng-select formControlName="methodType" placeholder="Select method type" [clearable]="false" class="bg-white">
            <ng-option value="GET">GET</ng-option>
            <ng-option value="POST">POST</ng-option>
          </ng-select>
        </form-errors-wrapper>
      </div>
      <div class="flex-grow-1">
        <div class="field-label-req">Content Type</div>
        <form-errors-wrapper label="Content Type" [control]="pullForm.controls['contentType']">
          <ng-select formControlName="contentType" placeholder="Select content type" [clearable]="false"
            class="bg-white">
            <ng-option value="form-data">form-data</ng-option>
            <ng-option value="x-www-form-urlencoded">x-www-form-urlencoded</ng-option>
            <ng-option value="application/json">application/json</ng-option>
          </ng-select>
        </form-errors-wrapper>
      </div>
    </div>
    <!-- cURL/Payload for Pull -->
    <div class="d-flex align-items-end mb-16" style="gap: 14px;">
      <div class="flex-grow-1">
        <div class="field-label">cURL/Payload</div>
        <form-errors-wrapper label="cURL/Payload" [control]="pullForm.controls['curlPayload']">
          <textarea formControlName="curlPayload" placeholder="enter curl/payload" rows="3" class="w-100"
            (input)="onPayloadChange()"></textarea>
        </form-errors-wrapper>
      </div>
      <button type="button" class="bg-accent-green w-120 h-40 text-white h5 border-0 br-4" (click)="extractFields()"
        [disabled]="isExtractingFields">
        <span *ngIf="!isExtractingFields">Extract Fields</span>
        <span *ngIf="isExtractingFields">Extracting...</span>
      </button>
    </div>
    <!-- Fields Status -->
    <div *ngIf="extractedFields && extractedFields.length > 0" class="mt-8 text-xs text-accent-green">
      ✓ {{extractedFields.length}} fields available for mapping
    </div>
    <!-- Payload Mapping for Pull -->
    <div class="mb-16">
      <div class="field-label-req">Payload Mapping</div>
      <div formArrayName="payloadMapping">
        <div *ngFor="let mapping of pullPayloadMapping.controls; let i = index" [formGroupName]="i">
          <div class="d-flex align-items-center mb-16" style="gap: 14px;">
            <div style="flex: 1; width: 50%;">
              <form-errors-wrapper label="Field Name" [control]="mapping.get('fieldName')">
                <ng-select formControlName="fieldName" [items]="formFields" placeholder="Select field name"
                  [clearable]="true" [trackByFn]="trackByFn" class="bg-white">
                </ng-select>
              </form-errors-wrapper>
            </div>
            <div style="flex: 1; width: 50%;">
              <form-errors-wrapper label="Mapped Value" [control]="mapping.get('mappedValue')">
                <!-- Show dropdown when fields are extracted -->
                <ng-select *ngIf="extractedFields && extractedFields.length > 0" formControlName="mappedValue"
                  [items]="extractedFields" placeholder="Select mapped value" [clearable]="true" class="bg-white">
                </ng-select>
                <!-- Show input when no fields are extracted -->
                <input *ngIf="!extractedFields || extractedFields.length === 0" type="text"
                  formControlName="mappedValue" placeholder="Extract fields or enter manually" />
              </form-errors-wrapper>
            </div>
            <!-- Hide delete button for first 2 fixed fields -->
            <div *ngIf="i >= 2" class="bg-light-red cursor-pointer"
              style="width: 29px; height: 29px; border-radius: 6px; display: flex; align-items: center; justify-content: center;"
              (click)="removePayloadMapping(i)">
              <span class="icon ic-delete ic-xxxs"></span>
            </div>
            <!-- Show placeholder for first 2 fields to maintain alignment -->
            <div *ngIf="i < 2" style="width: 29px; height: 29px;"></div>
          </div>
        </div>
      </div>
      <div class="cursor-pointer align-center fw-700 mt-12">
        <span class="icon ic-xs ic-add ic-accent-green"></span>
        <span class="text-accent-green ml-4" (click)="addPayloadMapping()">Add Additional payload</span>
      </div>
    </div>
    <!-- Query Parameters - Show only for GET method -->
    <div class="mb-16" *ngIf="pullForm.get('methodType')?.value === 'GET'">
      <div class="field-label">Query Parameters</div>
      <div formArrayName="queryParameters">
        <div *ngFor="let param of pullQueryParameters.controls; let i = index" [formGroupName]="i">
          <div class="d-flex align-items-center mb-16" style="gap: 14px;">
            <div class="flex-grow-1">
              <form-errors-wrapper label="Key" [control]="param.get('key')">
                <input type="text" formControlName="key" placeholder="field name" />
              </form-errors-wrapper>
            </div>
            <div class="flex-grow-1">
              <form-errors-wrapper label="Value" [control]="param.get('value')">
                <input type="text" formControlName="value" placeholder="mapped value" />
              </form-errors-wrapper>
            </div>
            <div class="bg-light-red cursor-pointer"
              style="width: 29px; height: 29px; border-radius: 6px; display: flex; align-items: center; justify-content: center;"
              (click)="removeQueryParameter(i)">
              <span class="icon ic-delete ic-xxxs"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="cursor-pointer align-center fw-700 mt-12">
        <span class="icon ic-xs ic-add ic-accent-green"></span>
        <span class="text-accent-green ml-4" (click)="addQueryParameter()">Add query parameters</span>
      </div>
    </div>
    <!-- Header Variables -->
    <div class="mb-16">
      <div class="field-label">Header Variables</div>
      <div formArrayName="headerVariables">
        <div *ngFor="let header of pullHeaderVariables.controls; let i = index" [formGroupName]="i">
          <div class="d-flex align-items-center mb-16" style="gap: 14px;">
            <div class="flex-grow-1">
              <form-errors-wrapper label="Key" [control]="header.get('key')">
                <input type="text" formControlName="key" placeholder="field name" />
              </form-errors-wrapper>
            </div>
            <div class="flex-grow-1">
              <form-errors-wrapper label="Value" [control]="header.get('value')">
                <input type="text" formControlName="value" placeholder="mapped value" />
              </form-errors-wrapper>
            </div>
            <div class="bg-light-red cursor-pointer"
              style="width: 29px; height: 29px; border-radius: 6px; display: flex; align-items: center; justify-content: center;"
              (click)="removeHeaderVariable(i)">
              <span class="icon ic-delete ic-xxxs"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="cursor-pointer align-center fw-700 mt-12">
        <span class="icon ic-xs ic-add ic-accent-green"></span>
        <span class="text-accent-green ml-4" (click)="addHeaderVariable()">Add header variables</span>
      </div>
    </div>
    <!-- Body Variables - Show only for POST method -->
    <div class="mb-16" *ngIf="pullForm.get('methodType')?.value === 'POST'">
      <div class="field-label">Body Variables</div>
      <div formArrayName="bodyVariables">
        <div *ngFor="let body of pullBodyVariables.controls; let i = index" [formGroupName]="i">
          <div class="d-flex align-items-center mb-16" style="gap: 14px;">
            <div class="flex-grow-1">
              <form-errors-wrapper label="Key" [control]="body.get('key')">
                <input type="text" formControlName="key" placeholder="field name" />
              </form-errors-wrapper>
            </div>
            <div class="flex-grow-1">
              <form-errors-wrapper label="Value" [control]="body.get('value')">
                <input type="text" formControlName="value" placeholder="mapped value" />
              </form-errors-wrapper>
            </div>
            <div class="bg-light-red cursor-pointer"
              style="width: 29px; height: 29px; border-radius: 6px; display: flex; align-items: center; justify-content: center;"
              (click)="removeBodyVariable(i)">
              <span class="icon ic-delete ic-xxxs"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="cursor-pointer align-center fw-700 mt-12">
        <span class="icon ic-xs ic-add ic-accent-green"></span>
        <span class="text-accent-green ml-4" (click)="addBodyVariable()">Add body variables</span>
      </div>
    </div>
  </form>
</div>
<!-- Footer Buttons -->
<div class="flex-end p-16 box-shadow-20">
  <button type="button" class="btn-gray mr-20" (click)="closeModal()">
    Cancel
  </button>
  <button type="button" class="btn-coal" (click)="onSubmit()">
    Add Account
  </button>
</div>