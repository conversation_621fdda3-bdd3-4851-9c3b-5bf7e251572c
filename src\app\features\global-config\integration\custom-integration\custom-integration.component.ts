import { Component, OnInit, OnD<PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { PAGE_SIZE } from 'src/app/app.constants';
import {
  FetchIntegrationList,
  FetchWebhook
} from 'src/app/reducers/Integration/integration.actions';
import { getWebhook } from 'src/app/reducers/Integration/integration.reducer';
import { NotificationsService } from 'angular2-notifications';

import { IntegrationService } from 'src/app/services/controllers/integration.service';
import { getTenantName } from 'src/app/core/utils/common.util';

@Component({
  selector: 'app-custom-integration',
  templateUrl: './custom-integration.component.html'
})
export class CustomIntegrationComponent implements OnInit, OnDestroy {
  private stopper: Subject<void> = new Subject<void>();
  activeTab: 'push' | 'pull' = 'push';
  pushForm: FormGroup;
  pullForm: FormGroup;
  modalRef: BsModalRef;
  receivedData: any;
  displayName: string;
  image: string;
  name: string;
  currPageNumber: number = 1;
  PageSize: number = PAGE_SIZE;
  isExtractingFields: boolean = false;
  extractedFields: any[] = [];
  tenantId: string = getTenantName();
  webhookConstantVariables: any[] = [];
  isLoadingWebhookVariables: boolean = false;

  get formFields(): string[] {
    if (!this.webhookConstantVariables || this.webhookConstantVariables.length === 0) {
      return [];
    }
    const filteredFields = this.webhookConstantVariables
      .map((field: string) => {
        return field.replace(/^#+|#+$/g, '');
      })
      .filter((field: string) => field && field.trim().length > 0);
    return filteredFields;
  }

  constructor(
    private fb: FormBuilder,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    private _notificationsService: NotificationsService,
    private _integrationService: IntegrationService,
    private cdr: ChangeDetectorRef
  ) {
    const storedData = localStorage.getItem('integrationData');
    if (storedData) {
      this.receivedData = JSON.parse(storedData);
      this.image = this.receivedData.image;
      this.displayName = this.receivedData.displayName;
      this.name = this.receivedData.name || this.receivedData.originalName;
    }
    this.initializeForms();
  }

  ngOnInit(): void {
    this.fetchWebhookConstantVariables();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }

  initializeForms(): void {
    this.pushForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      relationshipManagerEmail: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      additionalEmail: this.fb.array([]),
      bccEmail: this.fb.array([]),
      curlPayload: ['', Validators.required],
      payloadMapping: this.fb.array([])
    });

    this.pullForm = this.fb.group({
      webhookUrl: [''],
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      relationshipManagerEmail: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      additionalEmail: this.fb.array([]),
      bccEmail: this.fb.array([]),
      curlPayload: ['', Validators.required],
      payloadMapping: this.fb.array([]),
      methodType: [null, Validators.required],
      contentType: [null, Validators.required],
      queryParameters: this.fb.array([]),
      headerVariables: this.fb.array([]),
      bodyVariables: this.fb.array([])
    });
  }

  switchTab(tab: 'push' | 'pull'): void {
    if (this.activeTab !== tab) {
      this.activeTab = tab;
      const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
      const payloadArray = currentForm.get('payloadMapping') as FormArray;
      if (payloadArray.length === 0) {
        this.addDefaultPayloadMapping();
      }
    }
  }

  get pushRelationshipManagerEmail(): FormArray {
    return this.pushForm.get('relationshipManagerEmail') as FormArray;
  }

  get pushAdditionalEmail(): FormArray {
    return this.pushForm.get('additionalEmail') as FormArray;
  }

  get pushBccEmail(): FormArray {
    return this.pushForm.get('bccEmail') as FormArray;
  }

  get pushPayloadMapping(): FormArray {
    return this.pushForm.get('payloadMapping') as FormArray;
  }

  get pullRelationshipManagerEmail(): FormArray {
    return this.pullForm.get('relationshipManagerEmail') as FormArray;
  }

  get pullAdditionalEmail(): FormArray {
    return this.pullForm.get('additionalEmail') as FormArray;
  }

  get pullBccEmail(): FormArray {
    return this.pullForm.get('bccEmail') as FormArray;
  }

  get pullPayloadMapping(): FormArray {
    return this.pullForm.get('payloadMapping') as FormArray;
  }

  get pullQueryParameters(): FormArray {
    return this.pullForm.get('queryParameters') as FormArray;
  }

  get pullHeaderVariables(): FormArray {
    return this.pullForm.get('headerVariables') as FormArray;
  }

  get pullBodyVariables(): FormArray {
    return this.pullForm.get('bodyVariables') as FormArray;
  }

  addEmail(emailArray: FormArray, emailValue: string, inputElement: HTMLInputElement): void {
    const trimmedEmail = emailValue?.trim();
    if (trimmedEmail && this.validateEmail(trimmedEmail)) {
      const existingEmails = emailArray.value || [];
      if (!existingEmails.includes(trimmedEmail)) {
        emailArray.push(new FormControl(trimmedEmail));
        inputElement.value = '';
        emailArray.markAsUntouched();
        emailArray.updateValueAndValidity();
      }
    }
  }

  removeEmail(index: number, emailArray: FormArray): void {
    if (emailArray.length > 0) {
      emailArray.removeAt(index);
      emailArray.markAsUntouched();
      emailArray.updateValueAndValidity();
    }
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  getValidEmails(emailArray: FormArray): any[] {
    return emailArray.controls.filter(control => control.value && control.value.trim());
  }

  onEmailKeyPress(event: KeyboardEvent, emailArray: FormArray, inputElement: HTMLInputElement): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.addEmail(emailArray, inputElement.value, inputElement);
    }
  }

  addDefaultPayloadMapping(): void {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    const payloadArray = currentForm.get('payloadMapping') as FormArray;
    payloadArray.clear();
    const availableFields = this.formFields;
    const firstField = availableFields.find(field => field.toLowerCase().includes('name')) || availableFields[0] || null;
    const secondField = availableFields.find(field => field.toLowerCase().includes('mobile') || field.toLowerCase().includes('phone')) || availableFields[1] || null;
    const firstGroup = this.fb.group({
      fieldName: [firstField, Validators.required],
      mappedValue: [null, Validators.required]
    });
    const secondGroup = this.fb.group({
      fieldName: [secondField, Validators.required],
      mappedValue: [null, Validators.required]
    });
    payloadArray.push(firstGroup);
    payloadArray.push(secondGroup);
  }

  addPayloadMapping(): void {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    const payloadArray = currentForm.get('payloadMapping') as FormArray;

    payloadArray.push(this.fb.group({
      fieldName: [null, Validators.required],
      mappedValue: [null, Validators.required]
    }));
  }

  removePayloadMapping(index: number): void {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    const payloadArray = currentForm.get('payloadMapping') as FormArray;
    if (index >= 2 && payloadArray.length > 2) {
      payloadArray.removeAt(index);
    }
  }

  addQueryParameter(): void {
    this.pullQueryParameters.push(this.fb.group({
      key: ['', Validators.required],
      value: ['', Validators.required]
    }));
  }

  removeQueryParameter(index: number): void {
    this.pullQueryParameters.removeAt(index);
  }

  addHeaderVariable(): void {
    this.pullHeaderVariables.push(this.fb.group({
      key: ['', Validators.required],
      value: ['', Validators.required]
    }));
  }

  removeHeaderVariable(index: number): void {
    this.pullHeaderVariables.removeAt(index);
  }

  addBodyVariable(): void {
    this.pullBodyVariables.push(this.fb.group({
      key: ['', Validators.required],
      value: ['', Validators.required]
    }));
  }

  removeBodyVariable(index: number): void {
    this.pullBodyVariables.removeAt(index);
  }

  extractFields(): void {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    const curlPayload = currentForm.get('curlPayload')?.value;
    if (!curlPayload) {
      this._notificationsService.error('Please enter cURL/Payload first.');
      return;
    }
    this.isExtractingFields = true;
    this._integrationService.getCustomSourceProperties(this.tenantId, curlPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          this.isExtractingFields = false;
          if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
            this.extractedFields = response.data.map((field: any) => {
              if (typeof field === 'string') {
                return field;
              } else if (typeof field === 'object' && field !== null) {
                return field.name || field.key || field.fieldName || JSON.stringify(field);
              }
              return field;
            });
            this._notificationsService.success(`${this.extractedFields.length} fields extracted successfully.`);
            this.updatePayloadMappingWithExtractedFields();
          } else {
            this.extractedFields = [];
            this._notificationsService.error(response?.message || 'No fields found in the response.');
          }
        },
        error: (error) => {
          this.isExtractingFields = false;
          console.error('Error extracting fields:', error);
          this._notificationsService.error('Failed to extract fields from payload.');
        }
      });
  }

  updatePayloadMappingWithExtractedFields(): void {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    const payloadMappingArray = currentForm.get('payloadMapping') as FormArray;
    while (payloadMappingArray.length > 2) {
      payloadMappingArray.removeAt(2);
    }
    if (this.extractedFields.length > 2) {
      const additionalFieldsCount = this.extractedFields.length - 2;
      for (let i = 0; i < additionalFieldsCount; i++) {
        const mappingGroup = this.fb.group({
          fieldName: [null, Validators.required],
          mappedValue: [null, Validators.required]
        });
        payloadMappingArray.push(mappingGroup);
      }
    }
  }

  onPayloadChange(): void {
    this.extractedFields = [];
    this.addDefaultPayloadMapping();
  }

  trackByFn(_index: number, item: string): string {
    return item;
  }

  fetchWebhookConstantVariables(): void {
    this.isLoadingWebhookVariables = true;
    this._store.dispatch(new FetchWebhook());
    this._store
      .select(getWebhook)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isLoadingWebhookVariables = false;
        if (data && data.webhookConstantVariables) {
          this.webhookConstantVariables = data.webhookConstantVariables;
          this.addDefaultPayloadMapping();
          this.cdr.detectChanges();
          setTimeout(() => {
            this.cdr.detectChanges();
          }, 100);
        } else {
          this.webhookConstantVariables = [];
        }
      });
  }

  onSubmit(): void {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    Object.keys(currentForm.controls).forEach((field) => {
      const control = currentForm.get(field);
      if (control && control.invalid) {
        console.log(`Invalid field: ${field}`);
      }
    });
    if (currentForm.valid) {
      const formData = this.prepareFormData();
      this.saveIntegration(formData);
    } else {
      this.markFormGroupTouched(currentForm);
    }
  }

  prepareFormData(): any {
    const currentForm = this.activeTab === 'push' ? this.pushForm : this.pullForm;
    const formValue = currentForm.value;
    const customMapping: any = {};
    if (formValue.payloadMapping && Array.isArray(formValue.payloadMapping)) {
      formValue.payloadMapping.forEach((mapping: any) => {
        if (mapping.fieldName && mapping.mappedValue) {
          customMapping[mapping.fieldName] = mapping.mappedValue;
        }
      });
    }
    const queryParameters: any = {};
    if (formValue.queryParameters && Array.isArray(formValue.queryParameters)) {
      formValue.queryParameters.forEach((param: any) => {
        if (param.key && param.value) {
          queryParameters[param.key] = param.value;
        }
      });
    }
    const headerVariables: any = {};
    if (formValue.headerVariables && Array.isArray(formValue.headerVariables)) {
      formValue.headerVariables.forEach((header: any) => {
        if (header.key && header.value) {
          headerVariables[header.key] = header.value;
        }
      });
    }
    const bodyVariables: any = {};
    if (formValue.bodyVariables && Array.isArray(formValue.bodyVariables)) {
      formValue.bodyVariables.forEach((body: any) => {
        if (body.key && body.value) {
          bodyVariables[body.key] = body.value;
        }
      });
    }
    const sourceId = this.receivedData?.sourceId || this.receivedData?.id;
    const payload: any = {
      accountName: formValue.accountName,
      sourceId: sourceId,
      customMapping: customMapping,
      parameters: {},
      serviceProviderName: this.displayName || this.name,
      formName: formValue.accountName,
      toRecipients: formValue.relationshipManagerEmail || [],
      ccRecipients: formValue.additionalEmail || [],
      bccRecipients: formValue.bccEmail || [],
      loginEmail: formValue.loginEmail || "",
      methodType: this.activeTab === 'pull' ? formValue.methodType : "POST",
      baseURL: this.activeTab === 'pull' ? formValue.webhookUrl : "",
      queryParameters: queryParameters,
      headerVariables: headerVariables,
      bodyVariables: bodyVariables,
      contentType: this.activeTab === 'pull' ? formValue.contentType : "application/json",
      apiKey: "",
      secretKey: "",
      event: this.activeTab === 'push' ? 0 : 1 // 0 for push, 1 for pull
    };

    return payload;
  }

  saveIntegration(payload: any): void {
    if (!payload.sourceId) {
      this._notificationsService.error('Source ID is missing. Please try again.');
      return;
    }
    this._integrationService.addCustomIntegration(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response && response.succeeded) {
            this._notificationsService.success('Custom integration account added successfully!');
            this._store.dispatch(new FetchIntegrationList({
              LeadSource: this.receivedData?.value || this.receivedData?.name || this.receivedData?.id,
              PageSize: this.PageSize,
              PageNumber: this.currPageNumber
            }));
            this.closeModal();
          } else {
            this._notificationsService.error(response?.message || 'Failed to add custom integration account.');
          }
        },
        error: (error) => {
          console.error('Error saving custom integration:', error);
          this._notificationsService.error(
            error?.error?.message ||
            error?.message ||
            'Failed to add custom integration account. Please try again.'
          );
        }
      });
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormControl) {
        control.markAsTouched();
      } else if (control instanceof FormArray) {
        control.markAsTouched();
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  closeModal(): void {
    this.pushForm.reset();
    this.pullForm.reset();
    this.initializeForms();
    if (this.modalRef) {
      this.modalRef.hide();
    } else {
      this.modalService.hide();
    }
  }

}