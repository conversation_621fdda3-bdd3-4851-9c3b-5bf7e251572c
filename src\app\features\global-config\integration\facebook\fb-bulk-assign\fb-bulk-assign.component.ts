import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
} from 'src/app/core/utils/common.util';
import {
  FetchPriorityList,
  UpdateUserAssignment,
} from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'fb-bulk-assign',
  templateUrl: './fb-bulk-assign.component.html',
})
export class FbBulkAssignComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  closeModal: Function;
  gridApi: GridApi;
  allActiveUsers: any;
  activeUsers: any;
  allUserList: any;
  moduleId: string;
  fbAccountName: string;
  isForm: boolean = false;

  project: FormControl = new FormControl(null);
  location: FormControl = new FormControl(null);

  assignedUser: any = [];

  agencyName: FormControl = new FormControl(null);
  selectedAccountId: any;
  selectedAccountName: string;
  selectedAdName: string;
  isAdAccount: boolean;
  isFormAccount: boolean;
  assignedUserDetails: any;
  agencyAccountId: string;
  agencySource: any;
  selectedLeadSource: any;

  projectListDetails: Array<string> = [];
  filteredAccounts: any[] = [];
  accounts: any[] = [];
  agencyNameList: any;
  userList: any;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  params: any;
  pages: any[] = [];
  image: string = '../../../../assets/images/integration/facebook.svg';
  canEnableAllowDuplicates: boolean = false;
  canEnableAllowSecondaryUsers: boolean = false;
  getAssignedToDetails = getAssignedToDetails;

  @Input() canAllowSecondaryUsers: boolean;
  @Input() selectedIntegrations: any[];
  @Input() sameAsPrimaryUsers: boolean;
  @Input() sameAsSelectedUsers: boolean;
  @Input() sameAsAbove: boolean;
  @Input() assignedSecondaryUsers: any[] = [];
  @Input() assignedDuplicateUser: any[] = [];
  @Input() assignedPrimaryUsers: any[] = [];
  @Input() updatedIntegrationList: Array<any>;
  @Input() selectedCount: number;

  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
  ) { }

  ngOnInit(): void {
    this.selectedAccountId = this.gridApi
      ?.getSelectedNodes()
      ?.map((dataNodes: any) => dataNodes?.data?.id);
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    const selectAndPipe = (selector: any) =>
      this._store.select(selector).pipe(takeUntil(this.stopper));

    this._store.dispatch(new FetchPriorityList());
    selectAndPipe(getPriorityList).subscribe((data: any) => {
      const filteredData = data.filter(
        (item: any) => item.name === 'SubSource'
      );
      this.moduleId = filteredData.map((item: any) => item.id).join(', ');
    });

    selectAndPipe(getPermissions).subscribe((permissions: any) => {
      this.canAssign = permissions?.includes('Permissions.Integration.Assign');
      this.canAssignToAny = permissions?.includes(
        'Permissions.Users.AssignToAny'
      );
    });

    selectAndPipe(getAdminsAndReportees).subscribe((data: any) => {
      this.userList = data;
      this.activeUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.activeUsers = assignToSort(this.activeUsers, '');
      this.selectAllForDropdownItems(this.activeUsers);
    });

    selectAndPipe(getUsersListForReassignment).subscribe((data: any) => {
      this.allUserList = data;
      this.allActiveUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      this.selectAllForDropdownItems(this.allActiveUsers);
    });
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  openConfirmDeleteModal(dataName: string, id: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(id);
        }
      });
    }
  }

  removeSelection(id: string) {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((dataNodes: any) => dataNodes?.data?.id === id);
    this.gridApi?.deselectNode(node?.[0]);
    if (this.gridApi?.getSelectedNodes()?.length <= 0) this.modalService.hide();
  }

  bulkAssignAccount(): void {
    let payload: any = {
      entityIds: this.gridApi
        ?.getSelectedNodes()
        ?.map((dataNodes: any) => dataNodes?.data?.id),
      moduleId: this.moduleId,
      userIds: this.assignedUser.value,
    };

    this._store.dispatch(new UpdateUserAssignment(payload, null, true));
    this.closeModal();
    this.gridApi?.deselectAll();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
