import { Component, EventEmitter, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import {
  BASIC_INFO,
  BHK_NO,
  BHK_TYPE,
  LEAD_ADDITIONAL_INFO,
  LEAD_ENQUIRY_INFO,
  LEAD_MARTIAL_STATUS,
  LEAD_NRI,
  OTHERS,
  PAYMENT_MODES,
  PROPERTY_LIST,
  TRANSACTION_TYPE_LIST,
} from 'src/app/app.constants';
import {
  Facing,

  Profession,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { LeadSourceType } from 'src/app/core/interfaces/master-data.interface';
import {
  assignToSort,
  formatBudget,
  isEmptyObject,
  onlyNumbers,
  patchFormControlValue,
} from 'src/app/core/utils/common.util';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchAgencyNameList,
  FetchChannelPartnerList,
  FetchProjectList,
  FetchPropertyList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import { getActiveLeadIsLoading } from 'src/app/reducers/lead/lead.reducer';
import {
  FetchAreaUnitList,
  FetchLeadSourceList,
} from 'src/app/reducers/master-data/master-data.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';

import { BehaviorSubject, combineLatest } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs/operators';
import {
  FetchFields,
  FetchSelectedFields,
  UpdateForm,
} from 'src/app/reducers/fields/fields.action';
import {
  getFields,
  getSelectedFields,
} from 'src/app/reducers/fields/fields.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'lead-customisation',
  templateUrl: './lead-customisation.component.html',
})
export class LeadCustomisationComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  customForm: FormGroup;
  basicInfoFields: Array<any> = BASIC_INFO;
  enquiryInfoFields: Array<any> = LEAD_ENQUIRY_INFO;
  additionalInfoFields: Array<any> = LEAD_ADDITIONAL_INFO;
  martialStatusInfoFields: Array<any> = LEAD_MARTIAL_STATUS;
  nriInfoFields: Array<any> = LEAD_NRI;
  otherInfoFields: Array<any> = OTHERS;
  showEvents: boolean = false;
  isHideBasicInfo: boolean = false;
  isHideBasicFieldInfo: boolean = false;
  basicInfo: boolean = false;
  isHideEnquiryInfo: boolean = false;
  isHideEnquiryFieldInfo: boolean = false;
  enquiryInfo: boolean = false;
  isHideAdditionalInfo: boolean = false;
  isHideAdditionalFieldInfo: boolean = false;
  additionalInfo: boolean = false;
  isHideMaritalInfo: boolean = false;
  isHideMaritalFieldInfo: boolean = false;
  maritalFieldInfo: boolean = false;
  isHideNRIInfo: boolean = false;
  isHideNRIFieldInfo: boolean = false;
  NRIFieldInfo: boolean = false;
  isHideOtherInfo: boolean = false;
  isHideOtherFieldInfo: boolean = false;
  otherFieldInfo: boolean = false;
  selectedSection: string = 'Lead Info';
  screen = window;
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  selectedLeadId: any;
  selectedLeadInfo: any;
  addLeadForm: FormGroup;
  loggedInUserId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
  propertyTypeList: any[] = JSON.parse(localStorage.getItem('propertyType'));
  placesList: any[] = [];
  projectList: Array<string> = [];
  propertyList: Array<string> = [];
  agencyNameList: Array<string> = [];
  propSubTypes: Array<{ displayName: string }> = [];
  bhkTypeList: Array<string> = [];
  // propTypeImg: {} = PROPERTY_TYPE_LIST;
  bhkTypes: Array<string> = BHK_TYPE;
  bhkNoList: Array<string> = BHK_NO;
  leadSources: LeadSourceType;
  preferredCountries = ['in'];
  checkDuplicacy: boolean = false;
  checkAlternateNumDuplicacy: boolean = false;
  duplicateLead: any;
  duplicateAltLead: any;
  currFormValues: any;
  lowerBudgetInWords: string = '';
  upperBudgetInWords: string = '';
  hasInternationalSupport: boolean = false;
  canEdit: boolean = false;
  canUpdateInfo: boolean = false;
  canAssign: boolean = false;
  canViewComponent: boolean = false;
  numberEdited: boolean = true;
  alternateNumberEdited: boolean = true;
  budgetValidation: boolean = false;
  users: any;
  allUsers: any[];
  currencyList: any[] = [];
  defaultCurrency: string;
  countryCode: any[];
  timeZone: any[];
  primaryAgentList: Object[];
  secondaryAgentList: Object[];
  primaryUserList: Object[];
  secondaryUserList: Object[];
  subSourceList: any;
  subSources: any;
  isShowSubSource: boolean = false;
  isShowReferralFields: boolean = false;
  isShowManualLocation: boolean = false;
  isShowManualCustomerLocation: boolean = false;
  syncingCurrency: boolean = false;
  areaSizeUnits: Array<any>;
  carpetAreaConversion: string;
  isGlobalSourceEdit: boolean;
  canAdminEditSource: boolean;
  canEditSource: boolean = true;
  profession: Array<Profession | string> = Object.values(Profession).slice(
    1,
    10
  );
  channelPartnerList: Array<any>;
  patchFormControlValue = patchFormControlValue;
  isEmptyObject = isEmptyObject;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  isLeadSourceListLoading: boolean = true;
  isSubSourceListLoading: boolean = true;
  isAreaUnitLoading: boolean = true;
  projectListIsLoading: boolean = true;
  propertyListIsLoading: boolean = true;
  isAgencyNameListLoading: boolean = true;
  isChannelPartnerListLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isAdminAndReporteesLoading: boolean = true;
  isPostingData: boolean = false;
  activeLeadIsLoading: boolean = true;
  isNotesMandatory: boolean;
  isDualOwnershipEnabled: boolean = true;
  canViewLeadSource: boolean = false;
  isGlobalSettingsLoading: boolean = true;
  enquiredFor: string[] = [];
  bhkTypesSelected: string[] = [];
  bhkNoSelected: string[] = [];
  manualLocationsList: any[] = [];
  backupLocation: any[] = [];

  enquiredForList: Array<Object> = TRANSACTION_TYPE_LIST;
  propertyType: Array<any> = PROPERTY_LIST;
  PaymentModeList = PAYMENT_MODES;
  Facing = Facing;
  @ViewChild('contactNoInput') contactNoInput: any;
  @ViewChild('alternateNoInput') alternateNoInput: any;
  @ViewChild('referralNoInput') referralNoInput: any;
  @ViewChild('executiveNoInput') executiveNoInput: any;
  receivedCurrentPath: string;
  selectedFieldNames: any;
  currentActive: any = 0;
  CustomFields: any;
  isLabelClicked: { [key: string]: boolean } = {};
  selectedInfo: any;
  showLeftNav: boolean = true;

  constructor(
    private fb: FormBuilder,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    public router: Router,
    public metaTitle: Title,
    private activatedRoute: ActivatedRoute,
    private _notificationService: NotificationsService,
    private shareDataService: ShareDataService
  ) { }

  ngOnInit() {
    this.metaTitle.setTitle('CRM | Lead Customization');
    this.headerTitle.setLangTitle('SIDEBAR.lead-customization');

    this.customForm = this.fb.group({
      name: [true, Validators.required],
      contactNo: [true],
      alternateContactNo: [null],
      email: [null],
      referralName: [null],
      referralEmail: [null],
      referralContactNo: [null],

      enquiredFor: [null],
      basePropertyType: [null],
      subPropertyType: [null],
      noOfBHK: [null],
      bhkType: [null],
      budget: [null],
      // locality: [null],
      // city: [null],
      // state: [null],
      enquiredLocation: [null],
      carpetArea: [null],
      buildUpArea: [null],
      saleableArea: [null],
      possession: [null],
      preferredFloor: [null],
      property: [null],
      project: [null],
      agencyName: [null],
      channelPartnerName: [null],
      campaignName: [null],

      gender: [null],
      dateofBirth: [null],
      fatherName: [null],
      motherName: [null],
      religion: [null],
      age: [null],
      panNumber: [null],
      aadharNumber: [null],
      voterID: [null],
      drivingLicense: [null],
      referralDetails: [null],
      profession: [null],
      customerLocation: [null],
      industry: [null],
      companyName: [null],
      designation: [null],
      annualIncome: [null],
      workLocation: [null],
      foodPreference: [null],
      purposeofPurchase: [null],
      securityDeposit: [null],
      maintenanceCost: [null],
      downPayment: [null],
      modeofPayment: [null],
      facing: [null],
      noofParking: [null],
      currentAddress: [null],
      currentAddressType: [null],
      // not their html
      maritalStatus: [null],
      areYouNRI: [null],

      spouseName: [null],
      anniversaryDate: [null],
      spousePhoneNo: [null],
      spousealtPhoneNo: [null],
      spouseDOB: [null],
      noofChildren: [null],
      spouseProfession: [null],
      spouseCompanyName: [null],
      spouseDesignation: [null],
      spouseAnnualIncome: [null],
      spouseWorkLocation: [null],

      nriState: [null],
      nriCity: [null],
      nriPinCode: [null],
      nriLocality: [null],
      passportType: [null],
      visaStatus: [null],

      sourcingManager: [null],
      closingManager: [null],
      channelPartner: [null],
      executiveName: [null],
      executiveContactNo: [null],
      // customerLocality: [null],
      // customerCity: [null],
      // customerState: [null],
      teamHead: [null],

      // their html
      // referralAltPhoneNo
    });

    this._store.dispatch(new FetchLeadSourceList());
    this._store.dispatch(new FetchAdminsAndReportees());
    this._store.dispatch(new FetchUsersListForReassignment());
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchPropertyList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchAreaUnitList());
    this._store.dispatch(new FetchChannelPartnerList());
    this._store.dispatch(new FetchFields());
    this._store.dispatch(new FetchSelectedFields());

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;
        this.isGlobalSourceEdit = data?.isLeadSourceEditable;
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
        this.timeZone = data?.defaultTimeZone;
        this.isNotesMandatory =
          data?.leadNotesSetting?.isNotesMandatoryOnAddLead;
        if (this.isNotesMandatory) {
          this.addLeadForm?.controls['notes']?.setValidators([
            Validators.required,
          ]);
        } else {
          this.addLeadForm?.controls['notes']?.clearValidators();
        }
        this.addLeadForm?.patchValue({
          currency:
            this.selectedLeadInfo?.enquiry?.currency || this.defaultCurrency,
        });
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isGlobalSettingsLoading = isLoading;
      });
    this._store
      .select(getFields)
      .pipe(takeUntil(this.stopper))
      .subscribe((items: any) => {
        this.CustomFields = items;
        // this.customForm = this.fb.group(
        //   this.CustomFields.reduce((group: any, field: any) => {
        //     let value = null;

        //     // Set value for specific fields like 'name' and 'contactNo'
        //     if (field.controlName === 'name' || field.controlName === 'contactNo') {
        //       value = true;
        //     }

        //     // Add control to the form group
        //     group[field.controlName] = [value];  // Here we create the form control

        //     // Optionally log the field controlName and value for debugging

        //     return group;  // Return the updated form group
        //   }, {})
        // );
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.customForm.get('name').valueChanges.subscribe((value) => {
      if (value !== true) {
        this.customForm.get('name').setValue(true, { emitEvent: false });
      }
    });

    this.customForm.get('contactNo').valueChanges.subscribe((value) => {
      if (value !== true) {
        this.customForm.get('contactNo').setValue(true, { emitEvent: false });
      }
    });

    this.customForm.valueChanges.subscribe((data: any) => {
      this.isHideBasicFieldInfo = Object.keys(data).some(
        (key: string) => data[key] && this.isFieldVisible(key, data, BASIC_INFO)
      );

      this.isHideEnquiryFieldInfo = Object.keys(data).some(
        (key: string) =>
          data[key] && this.isFieldVisible(key, data, LEAD_ENQUIRY_INFO)
      );

      this.isHideAdditionalFieldInfo = Object.keys(data).some(
        (key: string) =>
          data[key] && this.isFieldVisible(key, data, LEAD_ADDITIONAL_INFO)
      );

      this.isHideMaritalFieldInfo = Object.keys(data).some(
        (key: string) =>
          data[key] && this.isFieldVisible(key, data, LEAD_MARTIAL_STATUS)
      );

      this.isHideNRIFieldInfo = Object.keys(data).some(
        (key: string) => data[key] && this.isFieldVisible(key, data, LEAD_NRI)
      );

      this.isHideOtherFieldInfo = Object.keys(data).some(
        (key: string) => data[key] && this.isFieldVisible(key, data, OTHERS)
      );
    });

    this._store
      .select(getSelectedFields)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.selectedInfo = data;
        this.selectedInfo?.forEach((contentItem: any) => {
          const controlName = contentItem?.controlName;
          if (this.customForm.get(controlName)) {
            this.customForm.get(controlName).patchValue(true);
            this.isLabelClicked[controlName] =
              contentItem.isRequiredField || false;
          }
        });
      });

    /* Fetching Places List */
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });
    const adminsWithReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAdminAndReporteesLoading = isLoading;
      });

    const allUsers$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
      });

    this._store
      .select(getActiveLeadIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.activeLeadIsLoading = isLoading;
      });

    const permissions$ = this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      allUsers: allUsers$,
      permissions: permissions$,
    }).subscribe(({ adminsWithReportees, allUsers, permissions }) => {
      this.allUsers = allUsers?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });
      if (
        permissions?.includes('Permissions.Leads.Create') &&
        this.router.url?.includes('add')
      )
        this.canViewComponent = true;
      if (permissions?.includes('Permissions.Leads.Update'))
        this.canEdit = true;
      if (this.canEdit && this.router.url?.includes('edit'))
        this.canViewComponent = true;
      if (permissions?.includes('Permissions.Leads.Assign'))
        this.canAssign = true;
      if (permissions?.includes('Permissions.Leads.UpdateSource'))
        this.canAdminEditSource = true;
      if (permissions?.includes('Permissions.Leads.ViewLeadSource'))
        this.canViewLeadSource = true;
      if (permissions?.includes('Permissions.Leads.UpdateBasicInfo'))
        this.canUpdateInfo = true;

      if (permissions?.includes('Permissions.Users.AssignToAny')) {
        this.users = allUsers;
      } else {
        this.users = adminsWithReportees;
      }

      let activeUsers = this.users?.filter((user: any) => user.isActive);
      this.primaryUserList = assignToSort(
        activeUsers,
        this.selectedLeadInfo?.assignTo
      );
      this.primaryAgentList = assignToSort(
        activeUsers,
        this.selectedLeadInfo?.assignTo
      );
      this.secondaryUserList = assignToSort(
        activeUsers,
        this.selectedLeadInfo?.secondaryUserId
      );
      this.secondaryAgentList = assignToSort(
        activeUsers,
        this.selectedLeadInfo?.secondaryUserId
      );
      this.primaryAgentList = this.primaryUserList.filter(
        (el: any) => !this.selectedLeadInfo?.secondaryUserId?.includes(el?.id)
      );
      this.secondaryAgentList = this.secondaryUserList.filter(
        (el: any) => !this.selectedLeadInfo?.assignTo?.includes(el?.id)
      );
    });
  }

  toggleCheckboxSelection(array: string[], value: string, event: MouseEvent) {
    if (array?.includes(value))
      array = array?.filter((enquiry: string) => enquiry !== value);
    else if (!array?.includes(value)) array.push(value);
    else event?.preventDefault();
    return array;
  }

  toggleAllCheckboxes(fields: any[]) {
    this.customForm.markAsDirty();
    const allChecked = this.allCheckboxesChecked(fields);
    fields.forEach((field) => {
      this.customForm.get(field.controlName).setValue(!allChecked);
    });
  }

  allCheckboxesChecked(fields: any[]): boolean {
    return fields?.every(
      (field) => this.customForm.get(field?.controlName).value
    );
  }

  updatePropertyType(controlName: string) {
    const subPropertyTypeControl = this.customForm.get('subPropertyType');
    const basePropertyTypeControl = this.customForm.get('basePropertyType');
    const noOfBHKControl = this.customForm.get('noOfBHK');
    const bhkTypeControl = this.customForm.get('bhkType');

    if (controlName === 'subPropertyType') {
      const newValue = subPropertyTypeControl.value;
      basePropertyTypeControl.setValue(newValue);
      noOfBHKControl.setValue(newValue);
      bhkTypeControl.setValue(newValue);
    } else if (controlName === 'basePropertyType') {
      const newValue = basePropertyTypeControl.value;
      subPropertyTypeControl.setValue(newValue);
      noOfBHKControl.setValue(newValue);
      bhkTypeControl.setValue(newValue);
    }
    if (controlName === 'noOfBHK') {
      const newValue = noOfBHKControl.value;
      bhkTypeControl.setValue(newValue);
      basePropertyTypeControl.setValue(newValue);
      subPropertyTypeControl.setValue(newValue);
    }
    if (controlName === 'bhkType') {
      const newValue = bhkTypeControl.value;
      noOfBHKControl.setValue(newValue);
      basePropertyTypeControl.setValue(newValue);
      subPropertyTypeControl.setValue(newValue);
    }
  }

  isFieldVisible(fieldName: string, formData: any, dataset: any[]): boolean {
    const field = dataset.find((item: any) => item.controlName === fieldName);
    return field && formData[fieldName] ? true : false;
  }

  updateSubSource(selectedSource?: any) {
    const leadSource = selectedSource || 'Direct';
    this.subSources = this.subSourceList[leadSource] || [];
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  getSelectedCountryCodeAlternateNo(): any {
    return this.alternateNoInput?.selectedCountry;
  }

  getSelectedCountryCodeReferralNo(): any {
    return this.referralNoInput?.selectedCountry;
  }

  getSelectedCountryCodeExecutiveNo(): any {
    return this.executiveNoInput?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const primaryNumber = this.addLeadForm?.get('contactNo')?.value;
      const alternativeNumber =
        this.addLeadForm?.get('alternateContactNo')?.value;
      if (numType == 'primary') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
        if (control.value && alternativeNumber) {
          if (control.value === alternativeNumber) {
            this.addLeadForm
              .get('alternateContactNo')
              ?.setErrors({ samePhoneNumber: true });
            this.addLeadForm.get('alternateContactNo')?.markAsTouched();
          } else {
            this.addLeadForm
              .get('alternateContactNo')
              ?.setErrors({ samePhoneNumber: false });
            this.addLeadForm.get('alternateContactNo')?.markAsPristine();
            this.addLeadForm.get('alternateContactNo')?.markAsUntouched();
          }
          this.addLeadForm.get('alternateContactNo')?.updateValueAndValidity();
          return null;
        }
      } else if (numType == 'alternative') {
        const input = document.querySelector(
          '.alternateNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeAlternateNo()?.dialCode;

        if (control.value && primaryNumber && control.value === primaryNumber) {
          return { samePhoneNumber: true };
        }
      } else if (numType == 'referral') {
        const input = document.querySelector(
          '.referralNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeReferralNo()?.dialCode;
      } else if (numType == 'executive') {
        const input = document.querySelector(
          '.executiveNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeExecutiveNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          (numType == 'primary'
            ? this.contactNoInput?.value
            : numType == 'alternative'
              ? this.alternateNoInput?.value
              : numType == 'referral'
                ? this.referralNoInput?.value
                : this.executiveNoInput?.value) || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  get isResidential(): boolean {
    return this.addLeadForm.get('propertyTypeId').value === 'Residential';
  }

  removeLocation(type: any) {
    switch (type) {
      case 'location':
        this.addLeadForm.value.locationId = null;
        this.addLeadForm.value.enquiredLocality = null;
        this.addLeadForm.value.enquiredCity = null;
        this.addLeadForm.value.enquiredState = null;
        break;
      case 'changeLocation':
        this.addLeadForm.patchValue({
          enquiredLocality: null,
          enquiredCity: null,
          enquiredState: null,
        });
        break;
      case 'changeLocality':
        this.addLeadForm.patchValue({
          locationId: null,
        });
        break;
    }
  }

  clearManualLocation(location: any) {
    this.manualLocationsList = this.manualLocationsList.filter(
      (manualLocation: any) =>
        JSON.stringify(manualLocation) != JSON.stringify(location)
    );
    this.addLeadForm?.markAsDirty();
  }

  addMoreLocation() {
    const { enquiredCity, enquiredLocality, enquiredState }: any =
      this.addLeadForm.value;
    if (
      !enquiredCity?.trim() &&
      !enquiredState?.trim() &&
      !enquiredLocality?.trim()
    ) {
      return;
    }
    const filteredLocation = this.manualLocationsList.filter(
      (location: any) => {
        return (
          enquiredCity?.trim() == location?.city &&
          enquiredLocality?.trim() == location?.locality &&
          enquiredState?.trim() == location?.state
        );
      }
    );

    if (filteredLocation?.length) return;
    this.manualLocationsList.push({
      city: enquiredCity?.trim(),
      locality: enquiredLocality?.trim(),
      state: enquiredState?.trim(),
    });
    this.removeLocation('changeLocation');
  }

  saveData() {
    const formData = this.customForm.value;

    const payload: any = {
      selectedFields: this.CustomFields.map(
        (field: { id: any; controlName: string | number }) => ({
          fieldId: field.id,
          isSelected: formData[field.controlName] || false,
          fieldName: field.controlName,
          isRequired:
            field.controlName === 'name' || field.controlName === 'contactNo'
              ? true
              : this.isLabelClicked[field.controlName]
                ? true
                : false,
        })
      ),
    };
    this._store.dispatch(new UpdateForm(payload));
  }

  scrollTo(section: string, index: number) {
    this.currentActive = index;
    let el = document.getElementById(section) as HTMLElement;

    el.scrollIntoView({ behavior: 'smooth' });
  }

  toggleLabel(field: string) {
    this.isLabelClicked[field] = !this.isLabelClicked[field];
  }
}
