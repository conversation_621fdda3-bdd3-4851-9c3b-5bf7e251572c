import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { map, take, takeUntil } from 'rxjs';

import { DAYS, EMPTY_GUID, VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { LeadSourceType } from 'src/app/core/interfaces/master-data.interface';
import { changeCalendar, getAssignedToDetails, patchTime, setTimeZoneTime, toggleValidation, validateAllFormFields } from 'src/app/core/utils/common.util';
import { getIsLeadCustomStatusEnabled } from 'src/app/reducers/lead/lead.reducer';
import { FetchLeadSourceList } from 'src/app/reducers/master-data/master-data.actions';
import { getLeadSource, getLeadSourceIsLoading } from 'src/app/reducers/master-data/master-data.reducer';
import { CustomStatus, getCustomStatusList, getCustomStatusListIsLoading } from 'src/app/reducers/status/status.reducer';
import { AddRetention, UpdateRetention, UpdateTeams } from 'src/app/reducers/teams/teams.actions';
import { getAllTeamsList, getAllTeamsListIsLoading, getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'lead-rotation-add-group',
  templateUrl: './lead-rotation-add-group.component.html',
})
export class LeadRotationAddGroupComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() users: any[] = [];
  @Input() allUsers: any[] = [];
  @Input() group: any = {};
  @Input() context: any;
  @Input() retentionList: any[] = [];
  @Input() leadRotationGroups: any[] = [];

  getAssignedToDetails = getAssignedToDetails;
  @Output() closeAddGroupWithId: EventEmitter<any> = new EventEmitter<any>();
  leadRotationAddGroupForm: FormGroup;
  leadRetentionAddGroupForm: FormGroup;
  isCustomStatusEnabled: boolean = false;
  customStatusList: CustomStatus[] = [];
  filteredCustomStatusList: CustomStatus[] = [];
  allCustomSubStatus: any[];
  subStatusList: any[];
  isCustomStatusListLoading: boolean = true;
  customSubStatusList: any[];
  allSubStatusList: any;
  masterLeadStatus: Array<any> = JSON.parse(
    localStorage.getItem('masterleadstatus') || '[]'
  );
  groupUsers: any[] = [];
  allTeams: any = [];
  days = DAYS;
  Teamusers: any[] = [];
  allTeamsIsLoading: boolean = true;
  isNewTeam: boolean = false;
  selectedTeamId: string | null = null;
  usedLeadSources: number[] = [];
  filteredLeadSources: any[] = [];
  rotationNumOptions = [
    { value: 1, label: '1 time' },
    { value: 2, label: '2 times' },
    { value: 3, label: '3 times' },
    { value: 4, label: '4 times' },
    { value: 5, label: '5 times' },
    { value: 6, label: '6 times' },
    { value: 7, label: '7 times' },
    { value: 8, label: '8 times' },
    { value: 9, label: '9 times' },
    { value: 10, label: '10 times' }
  ];
  userData: any;
  currentDate: Date = new Date();
  filteredMembersList: any[];
  filteredLeaderList: any[];
  leadSources: LeadSourceType;
  isSourceListLoading: boolean = true;
  teamManager: any;
  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>
  ) { }

  async ngOnInit(): Promise<void> {
    this.selectAllForDropdownItems(this.days);
    this.users = this.users.filter((user) => user.isActive);
    this.filteredMembersList = this.allUsers;
    this.filteredLeaderList = this.allUsers;
    this.leadRotationAddGroupForm = this.fb.group({
      teamName: [null, Validators.required],
      shiftTimeFrom: [null, Validators.required],
      shiftTimeTo: [null, [Validators.required]],
      rotationTime: [null, Validators.required],
      bufferTime: [null],
      rotationNum: [null, Validators.required],
      source: [null, Validators.required],
    });

    this.leadRotationAddGroupForm.get('teamName')?.valueChanges.subscribe((value) => {
      if (value && typeof value === 'object' && value.isTag) {
        this.isNewTeam = true;
      } else {
        this.isNewTeam = false;

        // Handle both object and string values
        let teamId = value;
        let teamName = value;

        if (typeof value === 'object' && value !== null) {
          teamId = value.id;
          teamName = value.teamName;
        }

        const selectedTeam = this.allTeams?.find((team: any) =>
          team.id === teamId ||
          team.id === teamName ||
          team.teamName === teamName
        );
        if (selectedTeam) {
          this.selectedTeamId = selectedTeam.id;
          this.teamManager = selectedTeam.manager;
          this.Teamusers = selectedTeam.users;
        } else {
          this.selectedTeamId = null;
          this.teamManager = null;
          this.Teamusers = [];
        }
      }
    });

    this.leadRetentionAddGroupForm = this.fb.group({
      team: [null, Validators.required],
      status: [null, Validators.required],
      subStatus: [null, Validators.required],
      shiftTimeFrom: [null, Validators.required],
      shiftTimeTo: [null, [Validators.required]],
      rotationTime: [null],
      rotationNum: [null],
      leadRotationEnabled: [false],
      days: [null, Validators.required],
    });
    if (this.context === 'rotation') {
      this.store.dispatch(new FetchLeadSourceList());
    }

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this.store
      .select(getLeadSource)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        this.leadSources = leadSource ??
          [...leadSource]?.sort((a: any, b: any) =>
            a?.displayName.localeCompare(b?.displayName)
          );
        this.updateFilteredLeadSources();
      });

    this.store
      .select(getLeadSourceIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSourceListLoading = isLoading;
      });

    this.store
      .select(getAllTeamsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allTeams = data;
        if (Object.keys(this.group)?.length) {
          this.teamManager = this.allTeams?.filter((team: any) => team.id === this.group.id)[0]?.manager;
          this.Teamusers = this.allTeams?.filter((team: any) => team.id === this.group.id)[0]?.users;
        }
      });
    this.store
      .select(getAllTeamsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.allTeamsIsLoading = loading;
      });
    this.leadRetentionAddGroupForm.get('team')?.valueChanges.subscribe((value: string) => {
      this.Teamusers = this.allTeams?.filter((team: any) => team.id === value)[0]?.users;
      this.teamManager = this.allTeams?.filter((team: any) => team.id === value)[0]?.manager;
    });

    if (
      (this.group.configurations?.[0]?.isSourceLevel === false)
    ) {

      toggleValidation(VALIDATION_CLEAR, this.leadRotationAddGroupForm, 'source');
    }


    if (Object.keys(this.group).length && this.context === 'rotation') {

      const isExistingTeam = this.allTeams?.some((team: any) => team.id === this.group?.id);

      if (!isExistingTeam) {
        this.allTeams = [
          ...this.allTeams,
          { id: this.group?.id, teamName: this.group?.name }
        ];
        this.leadRotationAddGroupForm.get('teamName')?.setValue(this.group?.name);
        this.selectedTeamId = this.group?.id;
      } else {
        this.leadRotationAddGroupForm.get('teamName')?.setValue(this.group?.name);
        this.selectedTeamId = this.group?.id;
      }

      let startTime = patchTime(this.group.configurations?.[0]?.startTime, this.userData?.timeZoneInfo?.baseUTcOffset);
      let endTime = patchTime(this.group.configurations?.[0]?.endTime, this.userData?.timeZoneInfo?.baseUTcOffset);
      this.leadRotationAddGroupForm.get('rotationTime').setValue(this.timeToMinutes(this.group.configurations?.[0]?.rotationTime));
      this.leadRotationAddGroupForm.get('bufferTime').setValue(this.timeToMinutes(this.group.configurations?.[0]?.bufferTime) !== 0 ? this.timeToMinutes(this.group.configurations?.[0]?.bufferTime) : null);
      this.leadRotationAddGroupForm.get('rotationNum').setValue(this.group?.configurations?.[0]?.noOfRotation);
      this.leadRotationAddGroupForm.get('shiftTimeFrom').setValue(startTime);
      this.leadRotationAddGroupForm.get('shiftTimeTo').setValue(endTime);
      if (Array.isArray(this.group?.configurations?.[0]?.leadSources)) {
        const sourceValues = this.group?.configurations?.[0]?.leadSources || [];
        this.leadRotationAddGroupForm.get('source').setValue(sourceValues);
      } else if (this.group?.configurations?.[0]?.leadSources) {
        const sourceValue = this.group?.configurations?.[0]?.leadSources;
        this.leadRotationAddGroupForm.get('source').setValue(Array.isArray(sourceValue) ? sourceValue : [sourceValue]);
      }
      this.groupUsers = this.group.assignmentGroup;
    } else if (Object.keys(this.group)?.length && this.context === 'retention') {
      this.leadRetentionAddGroupForm.get('team').setValue(this.group?.id);
      this.leadRetentionAddGroupForm.get('leadRotationEnabled').setValue(this.group?.isRotationEnabled);
      if (this.group?.configurations && Object.keys(this.group?.configurations)?.length) {
        let shiftTimeFrom = patchTime(this.group?.configurations?.[0]?.startTime, this.userData?.timeZoneInfo?.baseUTcOffset)
        let shiftTimeTo = patchTime(this.group?.configurations?.[0]?.endTime, this.userData?.timeZoneInfo?.baseUTcOffset)
        this.leadRetentionAddGroupForm.get('shiftTimeFrom').setValue(shiftTimeFrom);
        this.leadRetentionAddGroupForm.get('shiftTimeTo').setValue(shiftTimeTo);
        this.leadRetentionAddGroupForm.get('rotationNum').setValue(this.group?.configurations?.[0]?.noOfRotation ? this.group?.configurations?.[0]?.noOfRotation : null);

        this.leadRetentionAddGroupForm.get('days').setValue(this.group?.configurations?.[0]?.dayOfWeeks);
        this.leadRetentionAddGroupForm.get('rotationTime').setValue(this.timeToMinutes(this.group?.configurations?.[0]?.rotationTime));
        let statusIds = [...this.group?.statuses]?.map((status: any) => status.id);
        this.leadRetentionAddGroupForm.get('status').setValue(statusIds);
        let subStatusIds = [...this.group?.subStatuses]?.map((status: any) => status.id);
        this.leadRetentionAddGroupForm.get('subStatus').setValue(subStatusIds);
      }
    }

    this.leadRetentionAddGroupForm.get('leadRotationEnabled').valueChanges.subscribe((data: boolean) => {
      if (data) {
        toggleValidation(VALIDATION_SET, this.leadRetentionAddGroupForm, 'rotationNum', [Validators.required]);
        toggleValidation(VALIDATION_SET, this.leadRetentionAddGroupForm, 'rotationTime', [Validators.required]);
      } else {
        this.leadRetentionAddGroupForm.get('rotationNum').markAsUntouched();
        this.leadRetentionAddGroupForm.get('rotationTime').markAsUntouched();
        toggleValidation(VALIDATION_CLEAR, this.leadRetentionAddGroupForm, 'rotationNum');
        toggleValidation(VALIDATION_CLEAR, this.leadRetentionAddGroupForm, 'rotationTime');

      }
    });

    this.allSubStatusList = this.masterLeadStatus?.map((status: any) => status.childTypes || []).flat();
    const filterChildTypes = (status: any, retentionSubStatusIds: any[]) => {
      const hasValidChildTypes = status.childTypes && status.childTypes.length > 0;
      const filteredChildTypes = hasValidChildTypes
        ? status.childTypes.filter((child: any) => !retentionSubStatusIds.includes(child.id))
        : [];

      return {
        ...status,
        childTypes: filteredChildTypes,
        hasValidChildTypes: filteredChildTypes.length > 0,
      };
    };

    const filterStatuses = (status: any, retentionStatusIds: any[], statusIds: any[], isEditEnabled: boolean) => {
      if (status.hasValidChildTypes) {
        return status.childTypes.length > 0 || (isEditEnabled && statusIds.includes(status.id));
      } else {
        return !retentionStatusIds.includes(status.id) || (isEditEnabled && statusIds.includes(status.id));
      }
    };

    let retentionSubStatusIds = this.retentionList
      .flatMap((group: any) => group.subStatuses.map((subStatus: any) => subStatus.id));

    let retentionStatusIds = this.retentionList
      .flatMap((group: any) => group.statuses.map((status: any) => status.id));

    this.allSubStatusList = this.masterLeadStatus?.flatMap((status: any) => status.childTypes || []);
    let statusIds = this.group?.statuses?.map((status: any) => status.id) || [];

    this.masterLeadStatus = JSON.parse(
      localStorage.getItem('masterleadstatus') || '[]'
    ).filter((status: any) => status.displayName !== 'Invoiced');

    this.masterLeadStatus = this.masterLeadStatus
      .map((status: any) => filterChildTypes(status, retentionSubStatusIds))
      .filter((status: any) => filterStatuses(status, retentionStatusIds, statusIds, this.group?.isEditEnabled))
      .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));


    this.store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus.filter((item: any) => item.displayName !== 'Invoiced');
        this.customSubStatusList = this.customStatusList?.map(
          (status: any) => status.childTypes
        );
        if (this.customSubStatusList) {
          this.customSubStatusList = Object.values(this.customSubStatusList).flat();
        }
        this.customStatusList = this.customStatusList?.map((status: any) => {
          const hasValidChildTypes = status.childTypes && status.childTypes.length > 0;
          return {
            ...status,
            hasValidChildTypes: hasValidChildTypes,
          };
        });

        if (!this.group?.isEditEnabled) {
          let retentionSubStatusIds = this.retentionList.map((group: any) => {
            return group.subStatuses.map((subStatus: any) => subStatus.id);
          }).flat();
          let retentionStatusIds = this.retentionList.map((group: any) => {
            return group.statuses.map((status: any) => status.id);
          }).flat();
          this.filteredCustomStatusList = this.customStatusList?.filter((status: any) => {
            if (status.hasValidChildTypes) {
              const filteredChildTypes = status.childTypes.filter((child: any) => {
                return !retentionSubStatusIds.includes(child.id);
              });

              status.childTypes = filteredChildTypes;

              return filteredChildTypes.length > 0;
            } else {
              return !retentionStatusIds.includes(status.id);
            }
          });
        } else {
          let retentionSubStatusIds = this.retentionList.map((group: any) => {
            return group.subStatuses.map((subStatus: any) => subStatus.id);
          }).flat();

          let retentionStatusIds = this.retentionList.map((group: any) => {
            return group.statuses.map((status: any) => status.id);
          }).flat();

          let statusIds = [...this.group?.statuses]?.map((status: any) => status.id);

          this.filteredCustomStatusList = this.customStatusList?.filter((status: any) => {
            if (status.hasValidChildTypes) {
              const filteredChildTypes = status.childTypes.filter((child: any) => {
                return !retentionSubStatusIds.includes(child.id);
              });

              status.childTypes = filteredChildTypes;

              return filteredChildTypes.length > 0 || statusIds.includes(status.id);
            } else {
              return !retentionStatusIds.includes(status.id) || statusIds.includes(status.id);
            }
          });
        }
      });

    this.store
      .select(getCustomStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusListLoading = isLoading;
      });

    this.isCustomStatusEnabled = await this.store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();
    this.updateSubStatus();
    this.leadRetentionAddGroupForm.get('status').valueChanges.subscribe(() => {
      this.leadRetentionAddGroupForm.get('subStatus').reset();
    });
  }

  updateSubStatus() {
    let retentionStatusIds = this.retentionList.map((group: any) => {
      return group.subStatuses.map((subStatus: any) => subStatus.id);
    }).flat();

    if (!this.isCustomStatusEnabled) {
      if (this.leadRetentionAddGroupForm?.get('status')?.value?.length) {
        this.subStatusList = Object.values(this.allSubStatusList)
          .flat()
          .slice()
          .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName)).filter((subStatus: any) =>
            this.leadRetentionAddGroupForm?.get('status').value.includes(subStatus.baseId)
          );
      } else {
        this.subStatusList = [];
        this.subStatusList = Object.values(this.allSubStatusList)
          .flat()
          .slice()
          .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName));
      }

      if (!Object.keys(this.group).length) {
        this.subStatusList = this.subStatusList.filter((status: any) => {
          return !retentionStatusIds.includes(status.id);
        });
      } else {
        let subStatusIds = [...this.group?.subStatuses]?.map((status: any) => status.id);
        this.subStatusList = this.subStatusList.filter((status: any) => {
          return !retentionStatusIds.includes(status.id) || subStatusIds.includes(status.id);
        });
      }

    } else {
      if (this.leadRetentionAddGroupForm?.get('status')?.value?.length) {
        this.allCustomSubStatus = Object.values(this.customSubStatusList)
          .flat()
          .slice()
          .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName)).filter((subStatus: any) =>
            this.leadRetentionAddGroupForm?.get('status').value.includes(subStatus.baseId)
          );
      } else {
        this.allCustomSubStatus = [];
        this.allCustomSubStatus = Object.values(this.customSubStatusList)
          .flat()
          .slice()
          .sort((a: any, b: any) => a.displayName.localeCompare(b.displayName));
      }

      if (!Object.keys(this.group).length) {
        this.allCustomSubStatus = this.allCustomSubStatus.filter((status: any) => {
          return !retentionStatusIds.includes(status.id);
        });
      } else {
        let subStatusIds = [...this.group?.subStatuses]?.map((status: any) => status.id);
        this.allCustomSubStatus = this.allCustomSubStatus.filter((status: any) => {
          return !retentionStatusIds.includes(status.id) || subStatusIds.includes(status.id);
        });
      }
    }

    if (!this.allCustomSubStatus?.length && !this.subStatusList?.length) {
      this.leadRetentionAddGroupForm.get('subStatus').clearValidators();
      this.leadRetentionAddGroupForm.get('subStatus').markAsUntouched();
      this.leadRetentionAddGroupForm.get('subStatus').updateValueAndValidity();
    } else if (this.allCustomSubStatus?.length || this.subStatusList?.length) {
      this.leadRetentionAddGroupForm.get('subStatus').setValidators([Validators.required]);
      this.leadRetentionAddGroupForm.get('subStatus').updateValueAndValidity();

    }
  }

  shiftTimeFromValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (!value) {
        return { shiftTimeFrom: true };
      }
      return null;
    };
  }

  addCustomTeam(term: string) {
    return { teamName: term, id: term, isTag: true };
  }

  addGroup(group: string) {
    if (this.context === 'rotation') {
      if (!this.leadRotationAddGroupForm.valid) {
        validateAllFormFields(this.leadRotationAddGroupForm);
        return;
      }

      let selectedTeamValue = this.leadRotationAddGroupForm.get('teamName').value;
      let teamName = '';

      if (this.isNewTeam) {
        teamName = selectedTeamValue.teamName;
      } else {
        if (typeof selectedTeamValue === 'object' && selectedTeamValue !== null) {
          teamName = selectedTeamValue.teamName;
        } else {
          teamName = selectedTeamValue;
        }
      }
      const sourceValue = this.leadRotationAddGroupForm.get('source')?.value;
      let payload = {
        name: teamName,
        id: this.selectedTeamId,
        manager: this.teamManager?.id || this.group?.manager,
        userIds: this.Teamusers?.map((user: any) => user.id) || this.group?.userIds,
        configuration: {
          id: this.group.configurations?.length ? this.group.configurations?.[0]?.id : EMPTY_GUID,
          leadSources: Array.isArray(sourceValue)
            ? sourceValue
            : sourceValue
              ? [sourceValue]
              : null,
          noOfRotation: this.leadRotationAddGroupForm.get('rotationNum').value || this.leadRotationAddGroupForm.get('rotationNum').value,
          rotationTime: this.convertMinutesToFormat(Number(this.leadRotationAddGroupForm.get('rotationTime').value)),
          bufferTime: this.convertMinutesToFormat(Number(this.leadRotationAddGroupForm.get('bufferTime').value)),
          startTime: setTimeZoneTime(this.leadRotationAddGroupForm.get('shiftTimeFrom').value, this.userData?.timeZoneInfo?.baseUTcOffset),
          endTime: setTimeZoneTime(this.leadRotationAddGroupForm.get('shiftTimeTo').value, this.userData?.timeZoneInfo?.baseUTcOffset),
          isSourceLevel: true,
          integrationAccountIds: this.group?.configurations?.find((config: any) => config.isForRetention === false)?.integrationAccountIds || null,
        }
      };
      this.store.dispatch(new UpdateTeams(payload));
    }
    else if (this.context === 'retention') {
      if (!this.leadRetentionAddGroupForm.valid) {
        validateAllFormFields(this.leadRetentionAddGroupForm);
        return;
      }
      let retentionForm = this.leadRetentionAddGroupForm.value;
      let payload: any = {
        id: retentionForm.team,
        configuration: {
          noOfRotation: retentionForm.rotationNum ? retentionForm.rotationNum : 0,
          rotationTime: retentionForm.rotationTime ? this.convertMinutesToFormat(Number(this.leadRetentionAddGroupForm.get('rotationTime').value)) : null,
          startTime: setTimeZoneTime(retentionForm.shiftTimeFrom, this.userData?.timeZoneInfo?.baseUTcOffset),
          endTime: setTimeZoneTime(retentionForm.shiftTimeTo, this.userData?.timeZoneInfo?.baseUTcOffset),
          dayOfWeeks: retentionForm.days,

        },
        subStatusesIds: retentionForm?.subStatus ? [...retentionForm?.subStatus] : [],
        isRotationEnabled: retentionForm.leadRotationEnabled,
        statusesIds: [...retentionForm.status],
        statuses: [...(this.isCustomStatusEnabled ? this.customStatusList : this.masterLeadStatus)].filter((status: any) => [...retentionForm.status].includes(status?.id)),
        subStatuses: retentionForm?.subStatus ? [...(this.isCustomStatusEnabled ? this.allCustomSubStatus : this.subStatusList)].filter((subStatus: any) => [...retentionForm?.subStatus]?.includes(subStatus?.id)) : []
      };
      if (this.group.isEditEnabled) {
        payload.configuration.id = this.group?.configurations?.[0]?.id;
        payload.configuration.teamId = retentionForm?.team;
        this.store.dispatch(new UpdateRetention(payload));
      } else {
        this.store.dispatch(new AddRetention(payload));
      }
    }
    this.closeAddGroup(group);
  }

  timeToMinutes(timeString: string): number {
    if (timeString) {
      const [hours, minutes] = timeString?.split(':').map(Number);
      const totalMinutes = hours * 60 + minutes;
      return totalMinutes;
    }
    return null;
  }

  updateFilteredLeadSources(): void {
    if (!Array.isArray(this.leadSources)) {
      this.filteredLeadSources = [];
      return;
    }

    const allSourceList = this.leadRotationGroups
      ?.flatMap((team: any) => team?.configurations?.[0]?.leadSources || []) || [];

    const currentGroupSources = this.group?.configurations?.[0]?.leadSources || [];

    const isSourceInUse = (source: any): boolean => {
      const sourceValue = source.displayName;
      return allSourceList.includes(sourceValue) && !currentGroupSources.includes(sourceValue);
    };

    this.filteredLeadSources = this.leadSources.filter((source: any) => !isSourceInUse(source));
  }

  convertMinutesToFormat(durationInMinutes: number): string {
    const hours = Math.floor(durationInMinutes / 60);
    const remainingMinutes = durationInMinutes % 60;

    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = remainingMinutes.toString().padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}:00`;
  }

  closeAddGroup(group: any) {
    if (!Object.keys(group).length)
      group = null;
    Object.keys(this.leadRotationAddGroupForm.controls).forEach(key => {
      const control = this.leadRotationAddGroupForm.get(key);
      control.markAsPristine();
      control.markAsUntouched();
      control.updateValueAndValidity();
    });
    if (group)
      group.isEditEnabled = false;
    this.closeAddGroupWithId.emit(group);
  }

  RemoveUserFromGroup(groupUser: any) {
    let filteredUsers = this.groupUsers.filter((user) => user !== groupUser);
    this.groupUsers = filteredUsers;
    this.leadRotationAddGroupForm.get('teamUsers').setValue(filteredUsers);
    this.leadRotationAddGroupForm.markAsDirty();
    if (filteredUsers.length < 2) {
      this.leadRotationAddGroupForm.get('teamUsers').markAsTouched();
    }
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }
}
