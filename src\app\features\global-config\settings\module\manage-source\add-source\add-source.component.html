<div class="br-4">
  <h5 class="text-white fw-600 bg-black px-20 py-12">
    {{ (editMode ? 'Edit' : 'Add New') }} Source
  </h5>
  <form [formGroup]="sourceForm" autocomplete="off" class="pb-20 px-30">
    <div class="label-req">Source Name</div>
    <form-errors-wrapper [control]="sourceForm.controls['sourceName']" label="source name">
      <input type="text" appDebounceInput (debounceEvent)="doesSourceExist($event)" class="form-control"
        placeholder="Type here..." formControlName="sourceName">
    </form-errors-wrapper>
    <div class="">
      <div class="label">Source Logo</div>
      <div class="d-flex w-100 align-items-center">
        <div class="border w-100 br-4 p-10 d-flex align-items-center h-40" *ngIf="logoPreview">
          <div class="mr-10 d-flex align-items-center">
            <span class="icon ic-paper-clip ic-xs ic-black mr-8"></span>
            <span *ngIf="!isUploadingImage">{{ logoFileName | slice:0:20 }}</span>
            <span *ngIf="isUploadingImage" class="ml-8 text-primary">Uploading...</span>
            <!-- <span *ngIf="!isUploadingImage && uploadedImageURL" class="ml-8 text-success">✓ Uploaded</span> -->
          </div>
          <div class="d-flex ml-auto">
            <span class="text-primary cursor-pointer mr-10 text-decoration-underline"
              (click)="logoFileInput.click()" [ngClass]="{'disabled': isUploadingImage}">Replace</span>
            <span class="text-danger cursor-pointer text-decoration-underline"
              (click)="removeLogo()" [ngClass]="{'disabled': isUploadingImage}">Delete</span>
          </div>
        </div>
        <div *ngIf="!logoPreview" class="w-100 border br-4 p-10 cusrsor-pointer" (click)="logoFileInput.click()">
          <div class="align-center gap-2">
            <span class="icon ic-paper-clip ic-xs ic-black mr-8"></span>
            <span class="text-gray cursor-pointer">Select an image</span>
          </div>
        </div>
        <input type="file" #logoFileInput style="display: none;" (change)="onLogoSelected($event)" accept="image/*">
      </div>
    </div>
    <div class="d-flex gap-2 mt-16 justify-end bg-white position-sticky bottom-0">
      <button type="button" class="btn-gray" (click)="cancel()">Cancel</button>
      <button type="submit" class="btn-coal" (click)="save()" [disabled]="sourceForm.invalid || isUploadingImage">
        {{ editMode ? 'Update' : 'Add' }}
      </button>
    </div>
  </form>
</div>