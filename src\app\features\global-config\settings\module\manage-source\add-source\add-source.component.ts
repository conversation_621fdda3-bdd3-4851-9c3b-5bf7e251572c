import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NotificationsService } from 'angular2-notifications';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { AddSource, ExistSource, UpdateSource } from 'src/app/reducers/source/source.actions';
import { getSourceExist } from 'src/app/reducers/source/source.reducer';
import { Subject } from 'rxjs';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { takeUntil } from 'rxjs/operators';
import { FolderNamesS3 } from 'src/app/app.enum';

@Component({
  selector: 'add-source',
  templateUrl: './add-source.component.html'
})
export class AddSourceComponent implements OnInit, OnD<PERSON>roy {
  sourceForm: FormGroup;
  logoFile: File | null = null;
  logoPreview: string | null = null;
  logoFileName: string | null = null;
  editMode: boolean = false;
  sourceData: any = null;
  doesExistSourceName: boolean;
  uploadedImageURL: string | null = null;
  isUploadingImage: boolean = false;
  private stopper = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    public modalRef: BsModalRef,
    private notificationService: NotificationsService,
    private store: Store<AppState>,
    private blobStorageService: BlobStorageService
  ) { }

  ngOnInit(): void {
    this.initForm();
    if (this.sourceData) {
      this.editMode = true;
      this.patchFormValues();
    }
  }

  initForm(): void {
    this.sourceForm = this.fb.group({
      sourceName: [null, Validators.required]
    });
  }

  doesSourceExist(sourceName: string) {
    if (sourceName) {
      this.store.dispatch(new ExistSource(sourceName));
      this.store.select(getSourceExist).subscribe((doesExistSourceName: boolean) => {
        this.doesExistSourceName = doesExistSourceName;
        this.sourceForm.get('sourceName').setErrors(
          doesExistSourceName ? { alreadyExist: true } : null
        );
      });
    }
  }

  patchFormValues(): void {
    if (this.sourceData) {
      console.log(this.sourceData);
      this.sourceForm.patchValue({
        sourceName: this.sourceData.displayName
      });
      if (this.sourceData.imageURL) {
        this.logoPreview = this.sourceData.imageURL;
        this.logoFileName = this.sourceData.imageName
        this.uploadedImageURL = this.sourceData.imageURL;
      }
    }
  }

  onLogoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.logoFile = file;
      this.logoFileName = file.name;
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.logoPreview = e.target.result;
        this.uploadImageToBlobStorage(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  }

  uploadImageToBlobStorage(base64Image: string): void {
    this.isUploadingImage = true;
    this.uploadedImageURL = null;
    this.blobStorageService
      .uploadImageBase64([base64Image], FolderNamesS3.Images)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          this.isUploadingImage = false;
          if (response.data.length > 0) {
            this.uploadedImageURL = response.data[0];
          } else {
            this.notificationService.error('Failed to upload image. Please try again.');
          }
        },
        error: (error) => {
          this.isUploadingImage = false;
          this.notificationService.error('Failed to upload image. Please try again.');
        }
      });
  }

  removeLogo(): void {
    this.logoFile = null;
    this.logoPreview = null;
    this.logoFileName = null;
    this.uploadedImageURL = null;
  }

  save(): void {
    if (this.sourceForm.invalid) {
      return;
    }
    if (this.isUploadingImage) {
      this.notificationService.warn('Please wait for image upload to complete.');
      return;
    }
    const formData = {
      displayName: this.sourceForm.get('sourceName').value,
      // logoFile: this.logoFile,
      imageURL: this.uploadedImageURL,
      path:'source',
      leadSourceType: 0,
      isEnabled: true,
      isDefault: false,
    };
    try {
      if (this.editMode) {
        this.store.dispatch(new UpdateSource({
          ...formData,
          id: this.sourceData.id
        }));
      } else {
        this.store.dispatch(new AddSource(formData));
      }
      this.modalRef.hide();
    } catch (error) {
      this.notificationService.error('An error occurred. Please try again.');
    }
  }
  cancel(): void {
    this.modalRef.hide();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}