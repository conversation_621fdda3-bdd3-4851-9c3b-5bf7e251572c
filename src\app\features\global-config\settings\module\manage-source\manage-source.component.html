<div class="pt-20 px-30">
  <div class="bg-light-pearl">
    <div class="flex-between">
      <div class="align-center">
        <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-12" (click)="goBack()"></div>
        <span class="icon ic-circle-nodes ic-sm ic-blue-1150 mr-8"></span>
        <h5 class="fw-600 header-3">Manage Source</h5>
      </div>
      <div class="d-flex align-items-center btn-coal" (click)="openAddSourceModal()">
        <span class="ic-add icon ic-xxs"></span>
        <span class="ml-8 ip-d-none">Add New Source</span>
      </div>
    </div>
    <div class="pt-16">
      <div class="bg-white w-100 border-gray">
        <form autocomplete="off" class="align-center py-10 px-12 no-validation">
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
          <input placeholder="type to search" class="border-0 outline-0 w-100" autocomplete="off"
            [(ngModel)]="searchTerm" name="searchTerm" id="inpSearchCustomFlag"
            (ngModelChange)="updateFilteredSources()">
          <span *ngIf="searchTerm" class="icon ic-x-circle ic-sm ic-slate-90 cursor-pointer"
            (click)="clearSearch()"></span>
        </form>
      </div>
      <div class="px-16 py-12 bg-white">
        <div *ngIf="!sourcesLoading; else loadingTemplate">
          <div class="h-100-240 pinned-grid position-relative">
            <!-- No sources found message -->
            <div *ngIf="filteredSources.length === 0 && !sourcesLoading" class="flex-col flex-center h-100-270">
              <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
              <div class="fw-semi-bold text-xl text-mud">No sources found!</div>
              <p *ngIf="searchTerm" class="text-center mt-2">
                No sources match the search term "{{searchTerm}}". <a href="javascript:void(0)" (click)="clearSearch()"
                  class="text-accent-green">Clear search</a>
              </p>
            </div>
            <ag-grid-angular *ngIf="filteredSources.length > 0" #agGrid class="ag-theme-alpine w-100 h-100"
              [gridOptions]="gridOptions" [rowData]="filteredSources" [suppressPaginationPanel]="true"
              [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (gridReady)="onGridReady($event)"
              (selectionChanged)="onSelectionChanged($event)">
            </ag-grid-angular>
          </div>
          <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4" *ngIf="filteredSources?.length">
            <div class="mr-10 ip-mt-10">
              Showing {{(currOffset * pageSize) + 1}} to
              {{((currOffset * pageSize) + pageSize > totalCount ? totalCount : (currOffset * pageSize) + pageSize)}} of
              {{totalCount}} entries
            </div>
            <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
              <div class="flex-center">
                Entries per page
                <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" [searchable]="false"
                  ResizableDropdown class="w-80" (change)="onPageSizeChange()" [(ngModel)]="selectedPageSize">
                  <ng-option name="showEntriesSize" *ngFor="let size of pageSizeOptions" [value]="size">
                    {{size}}
                  </ng-option>
                </ng-select>
              </div>
              <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
              <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalCount, pageSize)"
                (pageChange)="onPageChange($event)" [isV2Pagination]="true">
              </pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #loadingTemplate>
  <div class="flex-center h-360">
    <application-loader></application-loader>
  </div>
</ng-template>

<ng-template #subSourcesPopup>
  <div class="p-20">
    <div class="align-center justify-between mb-3">
      <h4 class="m-0">Sub-Sources ({{selectedSubSources?.length}})</h4>
      <div class="cursor-pointer flex-center header-5 fw-400 text-red-350" (click)="modalRef.hide()">
        <span class="ic-close ic-xxs mb-2 ic-red-350 mr-4"></span>Close
      </div>
    </div>
    <div class="d-flex flex-wrap">
      <div *ngFor="let subSource of selectedSubSources"
        class="mr-10 mb-10 bg-light-pearl p-10 rounded-pill d-flex align-items-center">
        <span class="text-coal">{{subSource}}</span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #sourceWarningPopup>
  <div class="br-4 pb-20">
    <h2 class="h-100px align-center text-white fw-800 pl-20 brtr-4 brtl-4 waring-bg-pattern">
      <span *ngIf="!isBulkOperation">Are you sure you want to delete
        {{currentSource?.displayName}} source?</span>
      <span *ngIf="isBulkOperation">Are you sure you want to delete selected sources?</span>
    </h2>
    <div class="text-black-200 m-10 p-10 text-large br-4">
      Before proceeding, please assign
      <ng-container *ngIf="sourceCountData?.leadCount > 0">
        <span class="text-accent-green fw-600 text-decoration-underline cursor-pointer"
          (click)="navigateToLead()">{{sourceCountData?.leadCount}}</span> leads
      </ng-container>
      <ng-container *ngIf="sourceCountData?.leadCount > 0 && sourceCountData?.prospectCount > 0">
        and
      </ng-container>
      <ng-container *ngIf="sourceCountData?.prospectCount > 0">
        <span class="text-accent-green fw-600 text-decoration-underline cursor-pointer"
          (click)="navigateToData()">{{sourceCountData?.prospectCount}}</span> data
      </ng-container>
      currently associated with
      <span *ngIf="!isBulkOperation">this source</span>
      <span *ngIf="isBulkOperation">these sources</span>
      to a different source.
    </div>
    <div class="m-10">
      <h4 class="fw-600 text-red text text-decoration-underline ml-10">Important notes:</h4>
      <ul class="pl-20 pt-8">
        <li class="d-flex"><span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
          <div>
            Once deleted, no new leads can be created using
            <span *ngIf="!isBulkOperation">this source or its</span>
            <span *ngIf="isBulkOperation">these sources or their</span>
            associated sub-sources via integration.
          </div>
        </li>
        <li class="d-flex"><span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
          <div>
            New Leads/Data cannot be created using
            <span *ngIf="!isBulkOperation">this source or its</span>
            <span *ngIf="isBulkOperation">these sources or their</span>
            sub-sources.
          </div>
        </li>
        <li class="d-flex"><span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
          <div>
            <span *ngIf="!isBulkOperation">This source and its</span>
            <span *ngIf="isBulkOperation">These sources and their</span>
            sub-sources will also be excluded from all related reports.
          </div>
        </li>
      </ul>
    </div>
    <div class="flex-center mt-30 pr-10">
      <div class="p-8 fw-600 px-20 rounded bg-white border border-black mr-20 cursor-pointer"
        [ngClass]="{'opacity-50 pe-none': isToggleInProgress}" (click)="modalRef.hide()">
        Cancel
      </div>
      <div (click)="confirmSourceDisable()"
        class="p-8 fw-600 px-20 rounded bg-white border border-black cursor-pointer mr-20"
        [ngClass]="{'opacity-50 pe-none': isToggleInProgress}" data-automate-id="delete">
        Delete
      </div>
      <div (click)="convertToDirect()"
        class="btn btn-sm text-mud w-125 br-5 fw-600 flex-center text-large bg-black text-white"
        [ngClass]="{'opacity-50 pe-none': isToggleInProgress}" data-automate-id="convertYes">
        Convert To Direct
      </div>
    </div>
  </div>
</ng-template>