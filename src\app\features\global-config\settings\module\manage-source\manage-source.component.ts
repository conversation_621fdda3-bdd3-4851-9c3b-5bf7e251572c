import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil, combineLatest } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { environment } from 'src/environments/environment';

import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { Title } from '@angular/platform-browser';

import { GridOptions } from 'ag-grid-community';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { AddSourceComponent } from './add-source/add-source.component';

import { SourceActionsComponent } from './source-actions/source-actions.component';
import { SourceToggleComponent } from './source-toggle/source-toggle.component';
import {
  ConvertToDirect,
  DeleteSource,
  FetchSources,
  FetchSourcesWithSubSources,
  GetLeadDataCount,
  UpdateSourceStatus
} from 'src/app/reducers/source/source.actions';
import {
  getIsToggleInProgress,
  getSourceCountData,
  getSources,
  getSourcesLoading,
  getSourcesWithSubSources,
} from 'src/app/reducers/source/source.reducer';
import { getAssignedToDetails, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
@Component({
  selector: 'manage-source',
  templateUrl: './manage-source.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageSourceComponent implements OnInit, OnDestroy {
  @ViewChild('sourceWarningPopup') sourceWarningPopup: TemplateRef<any>;
  @ViewChild('subSourcesPopup') subSourcesPopup: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl: string = environment.s3ImageBucketURL;
  sources: any[] = [];
  filteredSources: any[] = [];
  selectedSubSources: string[] = [];
  sourcesLoading: boolean = false;
  searchTerm: string = '';
  currentSource: any = null;
  isToggleInProgress: boolean = false;
  sourceCountData: any = null;
  isBulkOperation: boolean = false;
  userData: any = null;
  allUsers: any[] = [];
  gridOptions: GridOptions;
  gridApi: any;
  currOffset: number = 0;
  pageSize: number = 10;
  selectedPageSize: number = 10;
  totalCount: number = 0;
  pageSizeOptions: number[] = [5, 10, 20, 50, 100];

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private headerTitle: HeaderTitleService,
    private notificationService: NotificationsService,
    private modalService: BsModalService,
    public metaTitle: Title,
    private cdr: ChangeDetectorRef,
    public modalRef: BsModalRef,
    private gridOptionsService: GridOptionsService
  ) { }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('Global Config');
    this.metaTitle.setTitle('CRM | Global Config');
    this.selectedPageSize = this.pageSize;
    this.initializeGridSettings();
    this.store.dispatch(new FetchSources());
    this.store.dispatch(new FetchSourcesWithSubSources());

    combineLatest([
      this.store.select(getSources),
      this.store.select(getSourcesWithSubSources)
    ]).pipe(takeUntil(this.stopper))
      .subscribe(([sources, sourcesWithSubSources]) => {
        if (sources && sources.length > 0) {
          this.sources = sources.map(source => {
            const sourceWithSubSources = sourcesWithSubSources?.find(s => {
              return s.leadSource === source.displayName
            });
            if (sourceWithSubSources) {
              return { ...source, subSources: sourceWithSubSources.subSources || [] };
            }
            return { ...source, subSources: [] };
          });
          this.updateFilteredSources();
          this.cdr.markForCheck();
        }
      });

    this.store
      .select(getSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.sourcesLoading = loading;
        this.cdr.markForCheck();
      });

    this.store
      .select(getIsToggleInProgress)
      .pipe(takeUntil(this.stopper))
      .subscribe((inProgress: boolean) => {
        this.isToggleInProgress = inProgress;
        this.cdr.markForCheck();
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((userData: any) => {
        this.userData = userData;
        this.cdr.markForCheck();
      });

    this.store.dispatch(new FetchUsersListForReassignment());
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.allUsers = users || [];
        this.cdr.markForCheck();
      });

    this.store
      .select(getSourceCountData)
      .pipe(takeUntil(this.stopper))
      .subscribe((countData: any) => {
        this.sourceCountData = countData;
        if (countData && this.currentSource) {
          this.checkSourceCountAndShowPopup();
        }
        this.cdr.markForCheck();
      });
  }

  initializeGridSettings(): void {
    this.setupGridOptions();
    this.setupColumnDefinitions();
    this.gridOptions.context = {
      componentParent: this
    };
  }

  setupGridOptions(): void {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.headerHeight = 40;
    this.gridOptions.rowClass = 'custom-row source-grid-row';
    this.gridOptions.domLayout = 'normal';
    this.gridOptions.rowSelection = 'multiple';
    this.gridOptions.suppressRowClickSelection = true;
    this.gridOptions.suppressNoRowsOverlay = true;
    this.gridOptions.suppressLoadingOverlay = true;
    this.gridOptions.defaultColDef = {
      ...this.gridOptions.defaultColDef,
      cellStyle: {
        'line-height': '58px',
        'padding-left': '15px',
        'padding-right': '15px'
      },
      cellClass: 'source-grid-cell'
    };
    this.gridOptions.onSelectionChanged = this.onSelectionChanged.bind(this);
  }

  setupColumnDefinitions(): void {
    this.gridOptions.columnDefs = [
      {
        headerName: 'Hide Source',
        field: 'toggle',
        maxWidth: 100,
        filter: false,
        sortable: false,
        lockPosition: true,
        valueGetter: (params: any) => [params.data?.isEnabled],
        cellRenderer: SourceToggleComponent,
        cellClass: 'source-toggle-cell'
      },
      {
        headerName: 'Source Logo',
        field: 'imageURL',
        maxWidth: 110,
        filter: false,
        sortable: false,
        valueGetter: (params: any) => params.data?.imageURL || '',
        cellRenderer: (params: any) => {
          console.log(params.data);
          return this.renderSourceLogo(params);
        },
        cellClass: 'source-logo-cell'
      },
      {
        headerName: 'Source Name',
        field: 'displayName',
        minWidth: 120,
        maxWidth: 150,
        filter: 'agTextColumnFilter',
        sortable: true,
        valueGetter: (params: any) => params.data?.displayName || params.data?.name || 'Unknown Source',
        cellRenderer: (params: any) => {
          return `<p class="source-name-text">${params.value || 'Unknown Source'}</p>`;
        },
        cellClass: 'source-name-cell'
      },
      {
        headerName: 'Sub Source',
        field: 'subSources',
        minWidth: 300,
        filter: 'agTextColumnFilter',
        sortable: true,
        valueGetter: (params: any) => params.data?.subSources || [],
        cellRenderer: (params: any) => {
          return this.renderSubSources(params);
        },
        onCellClicked: (params: any) => {
          this.handleSubSourceClick(params);
        },
        cellClass: 'sub-source-cell'
      },
      {
        headerName: 'Created',
        field: 'createdBy',
        minWidth: 200,
        filter: 'agTextColumnFilter',
        sortable: true,
        colId: 'CreatedOn',
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data?.createdBy, this.allUsers, true) || '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4 created-by-name">${params.value[0]}</p>
            <p class="created-by-date">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic created-by-timezone">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'created-by-cell'
      },
      {
        headerName: 'Modified',
        field: 'lastModifiedBy',
        minWidth: 200,
        filter: 'agTextColumnFilter',
        sortable: true,
        colId: 'LastModifiedOn',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data?.lastModifiedBy,
            this.allUsers,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4 modified-by-name">${params.value[0]}</p>
            <p class="modified-by-date">${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic modified-by-timezone">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'modified-by-cell'
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 120,
        maxWidth: 120,
        filter: false,
        sortable: false,
        suppressMovable: true,
        lockPosition: 'right',
        valueGetter: (params: any) => [params.data],
        cellRenderer: SourceActionsComponent,
        cellClass: 'source-actions-cell'
      }
    ];
  }

  updateFilteredSources(): void {
    let result = this.applySearchFilter([...this.sources]);
    result = this.applySorting(result);
    this.totalCount = result.length;
    this.filteredSources = this.applyPagination(result);
    this.updateGridData();
    this.cdr.markForCheck();
  }

  applySearchFilter(sources: any[]): any[] {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      return sources;
    }
    const searchTermLower = this.searchTerm.toLowerCase().trim();
    return sources.filter(source => this.matchesSearchTerm(source, searchTermLower));
  }

  matchesSearchTerm(source: any, searchTerm: string): boolean {
    const displayName = source.displayName || '';
    const name = source.name || '';
    const subSources = source.subSources || [];
    return displayName.toLowerCase().includes(searchTerm) ||
      name.toLowerCase().includes(searchTerm) ||
      subSources.some((subSource: string) => subSource.toLowerCase().includes(searchTerm));
  }

  applySorting(sources: any[]): any[] {
    return sources.sort((a, b) => {
      if (a.isEnabled && !b.isEnabled) return -1;
      if (!a.isEnabled && b.isEnabled) return 1;
      return a.displayName.localeCompare(b.displayName);
    });
  }

  applyPagination(sources: any[]): any[] {
    const startIndex = this.currOffset * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return sources.slice(startIndex, endIndex);
  }

  updateGridData(): void {
    if (this.gridApi) {
      this.gridApi.setRowData(this.filteredSources);
    }
  }

  getPages(totalCount: number, pageSize: number): number {
    return Math.ceil(totalCount / pageSize);
  }

  onPageChange(offset: number): void {
    this.currOffset = offset;
    this.updateFilteredSources();
  }

  onPageSizeChange(): void {
    this.pageSize = Number(this.selectedPageSize);
    this.currOffset = 0;
    this.updateFilteredSources();
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;

    if (this.filteredSources.length > 0) {
      this.gridApi.setRowData(this.filteredSources);
    }
    window.addEventListener('resize', () => {
      setTimeout(() => {
        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit();
        }
      });
    });

    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
    setTimeout(() => {
      if (this.gridApi) {
        this.gridApi.refreshCells({ force: true });
      }
    }, 100);
  }

  onSelectionChanged(_event: any): void {
    this.cdr.markForCheck();
  }

  toggleSourceVisibility(source: any): void {
    if (this.isToggleInProgress) {
      return;
    }
    this.currentSource = source;
    this.showToggleConfirmation(source);
  }

  checkSourceCountAndShowPopup(): void {
    if (this.sourceCountData && (this.sourceCountData.leadCount > 0 || this.sourceCountData.prospectCount > 0)) {
      this.showSourceWarningPopup();
    } else {
      this.showDeleteConfirmation(this.currentSource);
    }
  }

  showSourceWarningPopup(): void {
    this.modalRef = this.modalService.show(
      this.sourceWarningPopup,
      {
        class: 'modal-600 top-modal ph-modal-unset',
        ignoreBackdropClick: true
      }
    );
  }

  showToggleConfirmation(source: any): void {
    const actionText = source.isEnabled ? 'disable' : 'enable';
    const initialState = {
      message: 'GLOBAL.user-confirmation',
      confirmType: actionText,
      title: source.displayName,
      fieldType: 'source',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      {
        class: 'modal-400 top-modal ph-modal-unset',
        initialState,
        ignoreBackdropClick: false,
      }
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason === 'confirmed') {
          this.confirmToggleSource();
        }
      });
    }
  }

  confirmToggleSource(): void {
    if (this.currentSource) {
      this.isToggleInProgress = true;
      this.cdr.markForCheck();
      const payload = {
        sourceId: this.currentSource.id,
        isEnabled: !this.currentSource.isEnabled,
        sourceName: this.currentSource.displayName || this.currentSource.name
      };
      this.store.dispatch(new UpdateSourceStatus(this.currentSource.id, !this.currentSource.isEnabled));
    }
  }

  confirmSourceDisable(): void {
    if (this.sourceCountData && (this.sourceCountData.leadCount > 0 || this.sourceCountData.prospectCount > 0)) {
      this.notificationService.warn('Cannot delete source with associated leads. Please assign leads to another source or click Convert to Direct.');
      return;
    }
    this.confirmDeleteSource();
  }

  navigateToLead() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.router.navigate(['leads/manage-leads'], {
      queryParams: {
        Source: JSON.stringify(this.currentSource.displayName),
        isNavigatedFromSource: true,
      },
    });
  }

  convertToDirect() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.isToggleInProgress = true;
    this.cdr.markForCheck();
    if (this.currentSource) {
      const sourceValue = typeof this.currentSource.value === 'string'
        ? parseInt(this.currentSource.value, 10)
        : this.currentSource.value;
      this.store.dispatch(new ConvertToDirect(sourceValue));
    }
  }

  navigateToData() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.router.navigate(['data/manage-data'], {
      queryParams: {
        SourceValue: this.currentSource.value,
        isNavigatedFromSource: true,
      },
    });
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.currOffset = 0;
    this.updateFilteredSources();
    this.cdr.markForCheck();
  }

  openAddSourceModal(): void {
    this.modalRef = this.modalService.show(
      AddSourceComponent,
      {
        class: 'modal-350 top-modal ph-modal-unset',
        ignoreBackdropClick: true
      }
    );
  }

  editSource(source: any): void {
    this.modalRef = this.modalService.show(
      AddSourceComponent,
      {
        class: 'modal-350 top-modal ph-modal-unset',
        initialState: { sourceData: source },
        ignoreBackdropClick: true
      }
    );
  }

  deleteSource(source: any): void {
    if (source.isMasterSource) {
      this.notificationService.error('Master sources cannot be deleted.');
      return;
    }

    this.currentSource = source;
    this.isBulkOperation = false;
    this.store.dispatch(new GetLeadDataCount(source.value.toString()));
  }

  showDeleteConfirmation(source: any): void {
    const initialState = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: source.displayName,
      fieldType: 'source',
    };

    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      {
        class: 'modal-400 top-modal ph-modal-unset',
        initialState,
        ignoreBackdropClick: false,
      }
    );

    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason === 'confirmed') {
          this.confirmDeleteSource();
        }
      });
    }
  }

  confirmDeleteSource(): void {
    if (this.currentSource) {
      this.isToggleInProgress = true;
      this.cdr.markForCheck();
      const payload = {
        sourceId: this.currentSource.id,
        sourceName: this.currentSource.displayName || this.currentSource.name
      };
      this.store.dispatch(new DeleteSource(this.currentSource.id));
    }
  }

  goBack(): void {
    this.router.navigate(['/global-config']);
  }

  showAllSubSources(subSources: string[]): void {
    this.selectedSubSources = subSources || [];
    this.modalRef = this.modalService.show(
      this.subSourcesPopup,
      {
        class: 'modal-400 modal-dialog-centered ph-modal-unset',
        ignoreBackdropClick: false
      }
    );
  }

  renderSourceLogo(params: any): string {
    const sourceName = params.data?.displayName || params.data?.name || '';
    const firstChar = sourceName.charAt(0).toUpperCase();
    const bgColor = this.getRandomColor(sourceName);
    const imageUrl = params.imageURL || '';
    const hasValidImage = this.hasValidImageUrl(imageUrl);
    const fullImageUrl = this.getImageUrl(imageUrl, params.data);
    if (hasValidImage) {
      return `
        <div class="d-flex align-items-center source-logo-container">
          <img src="${fullImageUrl}" alt="${sourceName}" width="30" height="30" class="mr-2 rounded source-logo-image"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
          <div class="w-30px h-30px color-white br-4 mr-8 font-bold" style="display:none; background-color:${bgColor};
               justify-content:center; align-items:center;">
            <span>${firstChar}</span>
          </div>
        </div>`;
    } else {
      return `
        <div class="d-flex align-items-center source-logo-container">
          <div class="w-30px h-30px d-flex color-white mr-8 align-center justify-center font-bold br-4" style="background-color:${bgColor};">
            <span>${firstChar}</span>
          </div>
        </div>`;
    }
  }

  renderSubSources(params: any): string {
    const subSources: string[] = params.value || [];
    if (subSources.length === 0) {
      return '<p class="text-truncate">No sub-sources</p>';
    }
    const visibleSubSources = subSources.slice(0, 2);
    const remainingCount = subSources.length - 2;
    const subSourceTags = visibleSubSources.map(subSource =>
      `<div class="bg-light-pearl mr-8 px-8 py-4 br-20 ">
        <span class="text-coal text-sm text-truncate-1 break-all">${subSource}</span>
      </div>`
    ).join('');
    if (remainingCount > 0) {
      return `
        <div class="d-flex align-items-center br-5">
          ${subSourceTags}
          <div class="bg-accent-green d-flex align-items-center justify-content-center px-8 py-4 br-6 text-white cursor-pointer sub-source-more">
            <span class="text-decoration-underline text-sm sub-source-more-text" data-action="showMore">+${remainingCount} more</span>
          </div>
        </div>`;
    } else {
      return `<div class="d-flex align-items-center">${subSourceTags}</div>`;
    }
  }

  handleSubSourceClick(params: any): void {
    const action = params.event.target.getAttribute('data-action');
    if (action === 'showMore') {
      const subSources: string[] = params.data?.subSources || [];
      this.showAllSubSources(subSources);
    }
  }

  private hasValidImageUrl(imageURL: string): boolean {
    try {
      return imageURL && imageURL.trim && imageURL.trim() !== '';
    } catch (error) {
      console.error('Error validating image URL:', error);
      return false;
    }
  }

  getImageUrl(imageURL: string, source?: any): string {
    if (!imageURL) return '';
    if (imageURL.startsWith('http')) return imageURL;
    const IsLRSource = source?.IsLRSource|| false;

    if (IsLRSource) {
      return (this.s3BucketUrl || '') + imageURL;
    } else {
      return 'https://leadrat-black.s3.ap-south-1.amazonaws.com/' + imageURL;
    }
  }

  getRandomColor(text: string): string {
    if (!text) return '#3498db';
    const colors = [
      '#3498db', // Blue
      '#2ecc71', // Green
      '#e74c3c', // Red
      '#f39c12', // Orange
      '#9b59b6', // Purple
      '#1abc9c', // Turquoise
      '#d35400', // Pumpkin
      '#c0392b', // Pomegranate
      '#16a085', // Green Sea
      '#8e44ad', // Wisteria
      '#27ae60', // Nephritis
      '#2980b9', // Belize Hole
      '#f1c40f', // Sunflower
      '#e67e22'  // Carrot
    ];
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      hash = text.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % colors.length;
    return colors[index];
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
