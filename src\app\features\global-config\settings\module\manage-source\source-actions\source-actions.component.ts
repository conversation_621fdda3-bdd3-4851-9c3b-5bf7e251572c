import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'source-actions',
  templateUrl: './source-actions.component.html'
})
export class SourceActionsComponent {
  params: any;

  constructor() { }

  agInit(params: ICellRendererParams): void {
    this.params = params;
  }

  editSource(): void {
    if (this.params.context.componentParent.editSource) {
      this.params.context.componentParent.editSource(this.params.data);
    }
  }

  deleteSource(): void {
    if (this.params.context.componentParent.deleteSource) {
      this.params.context.componentParent.deleteSource(this.params.data);
    }
  }
}
