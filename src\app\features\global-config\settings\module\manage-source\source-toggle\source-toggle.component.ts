import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'source-toggle',
  templateUrl: './source-toggle.component.html'
})
export class SourceToggleComponent implements ICellRendererAngularComp {
  params: any;
  isEnabled: boolean = false;

  constructor() { }

  agInit(params: ICellRendererParams): void {
    this.params = params;
    // Check if this source is enabled
    this.isEnabled = params.data?.isEnabled || false;
  }

  refresh(params: ICellRendererParams): boolean {
    this.params = params;
    this.isEnabled = params.data?.isEnabled || false;
    return true;
  }

  onToggleChange(event: Event): void {
    // Prevent default checkbox behavior
    event.preventDefault();
    event.stopPropagation();

    // Call parent component's toggle method
    if (this.params.context.componentParent.toggleSourceVisibility) {
      this.params.context.componentParent.toggleSourceVisibility(this.params.data);
    }
  }
}
