import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { firstValueFrom, map, skipWhile, Subject, switchMap, take, takeUntil } from 'rxjs';
import {
  REPORT_FILTERS_KEY_LABEL,
  REPORTS_DATE_TYPE,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import { DataDateType, ReportDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getISODateFormat,
  getPages,
  onPickerOpened,
} from 'src/app/core/utils/common.util';
import { getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchCountryBasedCity,
  FetchProjectList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getCountryBasedCity,
  getCountryBasedCityLoading,
  getIsLeadCustomStatusEnabled,
  getProjectList,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchCityReports,
  FetchUserExportSuccess,
  UpdateCityFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getCityReports,
  getCityReportsFilter,
  getCityReportsTotalCount,
  getIsCityReportsLoading,
} from 'src/app/reducers/reports/reports.reducer';
import {
  CustomStatus,
  getCustomStatusList,
  getCustomStatusListIsLoading,
} from 'src/app/reducers/status/status.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-city-report',
  templateUrl: './city-report.component.html',
})
export class CityReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canExportReportees: boolean = false;
  canViewReportees: boolean = false;
  showLeftNav: boolean = true;
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  filtersPayload: any;
  users: Array<any> = [];
  allUsers: Array<any> = [];
  isAllUsersLoading: boolean = true;
  reportees: Array<any> = [];
  onlyReportees: Array<any> = [];
  isOnlyReporteesLoading: boolean = true;
  gridOptions: any;
  rowData: any;
  rowDataTotalCount: number;
  isCustomStatusEnabled: boolean = false;
  customStatusList: CustomStatus[] = [];
  columnDropDown: { field: string; hide: boolean }[] = [];
  canShowPercentage: boolean = false;
  gridApi: any;
  gridColumnApi: any;
  columns: any[];
  defaultColumns: any[];
  pageSize: number = 50;

  searchTerm: string;
  public searchTermSubject = new Subject<string>();
  getPages: any = getPages;
  isCityReportLoading: boolean = true;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  currOffset: number = 0;
  currentView: 'table' | 'graph' = 'table';
  advanceFilterForm: FormGroup;
  leadSources: any[] = [];
  subSourceList: any;
  allSubSourceList: any;
  allSubSourceListIsLoading: boolean = true;
  projectList: any;
  allCities: any;
  cities: any;
  citiesIsLoading: boolean;
  countries: any;
  countryBasedCityIsLoading: boolean;
  dateType: string;
  filterDate: any;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  showFilters: boolean = false;
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);
  onPickerOpened = onPickerOpened;
  currentDate: Date;
  userData: any;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  filteredColumnDefsCache: any[] = [];

  @ViewChild('reportsGraph') reportsGraph: any;
  globalSettingsData: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private fb: FormBuilder,
    private modalRef: BsModalRef
  ) { }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('SIDEBAR.reports');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this.initializeForms();
    this.initializeSubscriptions();
  }

  initializeForms() {
    this.advanceFilterForm = this.fb.group({
      UserIds: [null],
      Sources: [null],
      SubSources: [null],
      Projects: [null],
      Cities: [null],
      Countries: [null],
      ShouldShowAll: [false],
      ShouldShowPercentage: [false],
      DateType: [0],
      FromDate: [null],
      ToDate: [null],
      IsWithTeam: [false],
    });
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.filtersPayload = {
      ...this.filtersPayload,
      userStatus: visibility,
      pageNumber: 1,
    };

    if (isTopLevelFilter) {
      this.filtersPayload.users = null;
    }
    this.filterFunction();

    const filterUsers = (users: any[], visibility: any) => {
      switch (visibility) {
        case 1:
          return users?.filter((user) => user.isActive);
        case 2:
          return users?.filter((user) => !user.isActive);
        case null:
          return users;
        default:
          return users;
      }
    };

    if (this.canViewAllUsers) {
      this.allUsers = assignToSort(filterUsers(this.users, visibility), '');
    } else {
      this.onlyReportees = assignToSort(
        filterUsers(this.reportees, visibility),
        ''
      );
    }
  }

  filterFunction() {
    this.filtersPayload.pageNumber = 1;
    let filters = this.advanceFilterForm.value;
    if (
      filters?.DateType?.length ||
      filters?.FromDate?.length ||
      filters.UserIds?.length ||
      filters.Projects?.length ||
      filters.SubSources?.length ||
      filters.Sources?.length ||
      filters.Cities?.length ||
      filters.Countries?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      ...this.advanceFilterForm.value,
    };
    this._store.dispatch(new UpdateCityFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchCityReports());
  }

  async initializeSubscriptions() {
    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();

    this.globalSettingsData = await firstValueFrom(
      this._store
        .select(getGlobalSettingsAnonymous)
        .pipe(skipWhile((data) => !Object.keys(data).length))
    );

    this.isCustomStatusEnabled = await this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();

    if (this.isCustomStatusEnabled) {
      this._store
        .select(getCustomStatusList)
        .pipe(takeUntil(this.stopper))
        .subscribe((customStatus: any) => {
          this.customStatusList = customStatus;
        });

      this._store
        .select(getCustomStatusListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          if (!isLoading) this.initializeGridSettings();
          this.initializeGraphData();

        });
    }
    this.initializeGridSettings();
    this.initializeGraphData();

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.searchTermSubject.subscribe(() => {
      this.filtersPayload.pageNumber = 1;
      this.filtersPayload.SearchText = this.searchTerm;
      this.filterFunction();
    });

    this._store
      .select(getCityReportsFilter)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data };
        this.advanceFilterForm.patchValue({
          ...data,
        });
        this.pageSize = data?.pageSize;
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        } else if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });

    this._store
      .select(getCityReports)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data)?.length) {
          this.rowData = data.map((row: any) => {
            let statuses: any = {};
            row?.status?.forEach((status: any) => {
              statuses[status?.baseStatusDisplayName] = status?.count || 0;
              statuses[status?.baseStatusDisplayName + '__percentage__'] =
                `${status?.percentage}` || '';
            });
            return {
              ...row,
              statuses,
            };
          });
          let totalRow: any = {
            city: 'Total',
            statuses: {},
          };
          this.rowData.forEach((row: any) => {
            for (let key in row?.statuses) {
              if (!totalRow?.statuses?.[key]) {
                totalRow.statuses[key] = 0;
              }
              if (!key.includes('__percentage__'))
                totalRow.statuses[key] += row?.statuses?.[key] || 0;
            }
          });
          let totalCounts: any = {
            meetingDoneCount: 0,
            meetingDoneUniqueCount: 0,
            meetingNotDoneCount: 0,
            meetingNotDoneUniqueCount: 0,
            siteVisitDoneCount: 0,
            siteVisitDoneUniqueCount: 0,
            siteVisitNotDoneCount: 0,
            siteVisitNotDoneUniqueCount: 0,
            overdueCount: 0,
            allCount: 0,
            activeCount: 0,
          };
          this.rowData.forEach((row: any) => {
            Object.keys(totalCounts).forEach((key: string) => {
              totalCounts[key] += row[key];
            });
          });
          totalRow = {
            ...totalRow,
            ...totalCounts,
          };
          if (this.rowData?.length > 1) {
            this.rowData?.push(totalRow);
          }
        }
      });

    this._store
      .select(getCityReportsTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: number) => (this.rowDataTotalCount = count));

    this._store
      .select(getIsCityReportsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (isLoading: boolean) => (this.isCityReportLoading = isLoading)
      );

    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getCountryBasedCity)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countries = data?.countries
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.cities = data?.cityCountryList
          ?.filter((data: any) => data?.city)
          .slice()
          .sort((a: any, b: any) => a?.city.localeCompare(b?.city));
        this.allCities = [...this.cities];
      });
    this._store
      .select(getCountryBasedCityLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.countryBasedCityIsLoading = data));

    this.filterFunction();
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' &&
      this.reportsGraph?.isChartReady &&
      !this.reportsGraph?.showSelectionMessage;
  }

  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'Name'
    );
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    const nameAndLeads = [
      {
        headerName: 'Name',
        field: 'Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [
          params.data?.city ? params.data?.city : params.data?.name,
        ],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params.value[0]}
            </p></div>`;
        },
      },
      {
        headerName: 'All Leads',
        field: 'All Leads',
        filter: false,
        valueGetter: (params: any) => [
          params?.data?.allCount,
          params?.data?.activeCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `${params?.value?.[3] == 'Total' || params.value[0] == 0
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`
            }
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != 'Total'
              ? `<a>${params.value[1]}</a>`
              : params.value[1]
                ? params.value[1]
                : '--'
            }<span>
          </p>`
            : `${params?.value?.[3] == 'Total' || params.value[0] == 0
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><span >${params.value[0] ? params.value[0] : '--'
              }</span></p>`
            }
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != 'Total'
              ? `<span>${params.value[1]}</span>`
              : params.value[1]
                ? params.value[1]
                : '--'
            }<span>
          </p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (event.data.city == 'Total') {
            return;
          } else if (event.event.target.innerText == event.value[0]) {
            if (isCtrlClick) {
              this.getDataInNewTab('All Leads', params);
              return;
            }
            this.getDataFromCell('All Leads', event);
          } else if (event.event.target.innerText == event.value[1]) {
            if (isCtrlClick) {
              this.getDataInNewTab('Active Leads', params);
              return;
            }
            this.getDataFromCell('Active Leads', event);
          }
        },
      },
    ];
    const newAndPending: any = [
      {
        headerName: 'New',
        field: 'New',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.new,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.new__percentage__
            : '',
        ],
        minWidth: 70,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('New', params);
              return;
            }
            this.getDataFromCell('New', event);
          }
        },
      },
      {
        headerName: 'Pending',
        field: 'Pending',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.pending,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.pending__percentage__
            : '',
        ],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Pending', params);
              return;
            }
            this.getDataFromCell('Pending', event);
          }
        },
      },
    ];
    const overdue: any = [
      {
        headerName: 'Overdue',
        field: 'Overdue',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params?.data?.overdueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage ? params?.data?.overdueCountPercentage : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Overdue', params);
              return;
            }
            this.getDataFromCell('Overdue', event);
          }
        },
      },
    ];
    const callbackAndMS: any = [
      {
        headerName: 'Callback',
        field: 'Callback',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.callback,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.callback__percentage__
            : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`
              : `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Callback', params);
              return;
            }
            this.getDataFromCell('Callback', event);
          }
        },
      },
      {
        headerName: 'Meeting Scheduled',
        field: 'Meeting Scheduled',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.meeting_scheduled,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.meeting_scheduled__percentage__
            : '',
        ],
        minWidth: 140,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a ><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></a>`
            : `<span><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Meeting Scheduled', params);
              return;
            }
            this.getDataFromCell('Meeting Scheduled', event);
          }
        },
      },
    ];
    const meetingDoneAndNotDone: any = [
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params?.data?.meetingDoneCount,
          params?.data?.meetingDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params?.data?.meetingDoneUniqueCountPercentage
            : '',
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a ><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab('All Leads', params, 'Meeting Done');
              return;
            }
            this.getMeetingCount('All Leads', event, 'Meeting Done');
          }
        },
      },
      {
        headerName: 'Meeting Not Done',
        field: 'Meeting Not Done',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params?.data?.meetingNotDoneCount,
          params?.data?.meetingNotDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params?.data?.meetingNotDoneUniqueCountPercentage
            : '',
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Meeting Not Done'
              );
              return;
            }
            this.getMeetingCount('All Leads', event, 'Meeting Not Done');
          }
        },
      },
    ];
    const svs: any = [
      {
        headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.site_visit_scheduled,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.site_visit_scheduled__percentage__
            : '',
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a ><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></a>`
            : `<span><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', params);
              return;
            }
            this.getDataFromCell(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', event);
          }
        },
      },
    ];
    const siteVisitDoneAndNotDone: any = [
      {
        headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Taken' : 'Site Visit Done',
        field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Taken' : 'Site Visit Done',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params?.data?.siteVisitDoneCount,
          params?.data?.siteVisitDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params?.data?.siteVisitDoneUniqueCountPercentage
            : '',
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Site Visit Done'
              );
              return;
            }
            this.getMeetingCount('All Leads', event, 'Site Visit Done');
          }
        },
      },
      {
        headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Not Taken' : 'Site Visit Not Done',
        field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Not Taken' : 'Site Visit Not Done',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params?.data?.siteVisitNotDoneCount,
          params?.data?.siteVisitNotDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params?.data?.statuses?.siteVisitNotDoneUniqueCountPercentage
            : '',
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Site Visit Not Done'
              );
              return;
            }
            this.getMeetingCount('All Leads', event, 'Site Visit Not Done');
          }
        },
      },
    ];
    const others: any = [
      {
        headerName: 'Booked',
        field: 'Booked',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.booked,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.booked__percentage__
            : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Booked', params);
              return;
            }
            this.getDataFromCell('Booked', event);
          }
        },
      },
      {
        headerName: 'Invoiced',
        field: 'Invoiced',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.invoicedLeadsCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.invoicedLeadsCountPercentage
            : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Invoiced', params);
              return;
            }
            this.getDataFromCell('Invoiced', event);
          }
        },
      },
      {
        headerName: 'Booking Cancel',
        field: 'Booking Cancel',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.booking_cancel,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params?.data?.statuses?.booking_cancel__percentage__
            : '',
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Booking Cancel', params);
              return;
            }
            this.getDataFromCell('Booking Cancel', event);
          }
        },
      },
      {
        headerName: 'Not Interested',
        field: 'Not Interested',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.not_interested,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.not_interested__percentage__
            : '',
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Not Interested', params);
              return;
            }
            this.getDataFromCell('Not Interested', event);
          }
        },
      },
      {
        headerName: 'Dropped',
        field: 'Dropped',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.dropped,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.dropped__percentage__
            : '',
        ],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Dropped', params);
              return;
            }
            this.getDataFromCell('Dropped', event);
          }
        },
      },
      {
        headerName: 'Expression Of Interest',
        field: 'Expression Of Interest',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.statuses?.expression_of_interest,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.statuses?.expression_of_interest__percentage__
            : '',
        ],
        minWidth: 160,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return `<p>${params.value[0]
            ? params.value[0] +
            (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
            : '--'
            }</p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.city == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              window?.open(
                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[0]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=Dropped&filtersPayload=${encodeURIComponent(
                  JSON.stringify(filters)
                )}`,
                '_blank'
              );
              return;
            }
            this.getDataFromCell('Expression Of Interest', event);
          }
        },
      },
    ];
    this.gridOptions.columnDefs = this.isCustomStatusEnabled
      ? [
        ...nameAndLeads,
        // ...newAndPending,
        ...overdue,
        // ...callbackAndMS,
        ...meetingDoneAndNotDone,
        // ...svs,
        ...siteVisitDoneAndNotDone,
        // ...others
      ]
      : [
        ...nameAndLeads,
        ...newAndPending,
        ...overdue,
        ...callbackAndMS,
        ...meetingDoneAndNotDone,
        ...svs,
        ...siteVisitDoneAndNotDone,
        ...others,
      ];
    if (this.isCustomStatusEnabled)
      this.customStatusList.forEach((customStatus: CustomStatus) => {
        let col: any = {
          headerName: customStatus?.displayName,
          field: customStatus?.displayName,
          filter: false,
          hide: false,
          valueGetter: (params: any) => [
            params?.data?.statuses?.[customStatus?.status],
            params?.data?.userId,
            params?.data?.projectTitle,
            this.canShowPercentage
              ? params?.data?.statuses?.[
              customStatus?.status + '__percentage__'
              ]
              : '',
          ],
          minWidth: 110,
          cellRenderer: (params: any) => {
            const filters = { ...this.filtersPayload };
            if (filters?.IsWithTeam) filters.IsWithTeam = false;
            return params?.value?.[2] == 'Total' || params.value[0] == 0
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : !this.isCustomStatusEnabled
                ? `<p><a>${params.value[0]
                  ? params.value[0] +
                  (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                  : '--'
                }</a></p>`
                : `<p><span>${params.value[0]
                  ? params.value[0] +
                  (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                  : '--'
                }</span></p>`;
          },
          cellClass: 'cursor-pointer',
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.city == 'Total') {
              return;
            } else if (event.value[0] != 0) {
              if (isCtrlClick) {
                this.getDataInNewTab(customStatus?.displayName, params);
                return;
              }
              this.getDataFromCell(customStatus?.displayName, event);
            }
          },
        };
        this.gridOptions?.columnDefs?.push(col);
      });

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getDataFromCell(operation: string, event: any) {
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType =
      ReportDateType[Number(this.filtersPayload?.dateType)];
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload, Cities: [event?.data?.city] };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = filters;
  }

  getDataInNewTab(operation: string, params: any) {
    const filters = { ...this.filtersPayload, Cities: [params?.city] };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  getMeetingCountnewTab(operation: string, params: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload, Cities: [params?.city] };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  getMeetingCount(operation: string, event: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload, Cities: [event?.data?.city] };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }

    this.router.navigate(['leads/manage-leads']);
    let visitMeeting: any = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.dateType =
      ReportDateType[Number(this.filtersPayload?.dateType)];
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = filters;
    this.gridOptionsService.meetingStatus = visitMeeting;
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
    this.gridOptions.api = params.api;
  }

  toggleColumns(params: any): void {
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.getColDef()?.headerName,
        value: column,
      };
    });

    this.columns = [...this.columns.slice(1, this.columns?.length)].sort(
      (a: any, b: any) => a?.label?.localeCompare(b?.label)
    );
    this.defaultColumns = this.columns?.filter(
      (col) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myDataColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('status-reports-columns')?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnsSelected(columns: any): void {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('status-reports-columns', colData?.toString());
    const cols = columns?.map((col: any) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    if (columnState && columnState[0]) columnState[0].pinned = 'left';
    localStorage.setItem('myDataColumnState', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }
  refresh() {
    this.gridOptions.api?.refreshCells();
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  exportLeadReport() {
    this._store.dispatch(new FetchUserExportSuccess(''));
    this.filterFunction();
    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        selectedColumns: this.gridColumnApi
          .getColumnState()
          .filter((col: any) => !col?.hide)
          .map((col: any) => col?.colId)?.map((col: any) => {
            if (col === 'Referral Taken') {
              return 'Site Visit Done'
            }
            else if (col === 'Referral Not Taken') {
              return 'Site Visit Not Done'
            }
            else if (col === 'Referral Scheduled') {
              return 'Site Visit Scheduled'
            }
            return col
          }),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  assignCount() {
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.currOffset = 0;
    this.filterFunction();
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchCountryBasedCity());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  updateSubSource(): void {
    const sourceIds = this.advanceFilterForm.get('Sources')?.value;

    if (sourceIds?.length) {
      this.subSourceList = [];

      const selectedSource = this.leadSources?.filter((source: any) =>
        sourceIds.includes(source.id)
      );

      selectedSource.forEach((source: any) => {
        const subSource = this.allSubSourceList[source?.displayName];
        if (Array.isArray(subSource)) {
          this.subSourceList = [...this.subSourceList, ...subSource];
        }
      });
    } else {
      // If no SourceIds are selected, return all subSources
      this.subSourceList = Object.values(this.allSubSourceList).flat();
    }
  }

  dateChange(): void {
    if (this.dateType && this.filterDate?.[0]) {
      const fromDate = String(this.filterDate?.[0])?.includes('00.000Z')
        ? this.filterDate?.[0]
        : getISODateFormat(this.filterDate?.[0]);

      const toDate = String(this.filterDate?.[1])?.includes('00.000Z')
        ? this.filterDate?.[1]
        : getISODateFormat(this.filterDate?.[1]);

      this.advanceFilterForm.patchValue({
        DateType: DataDateType[this.dateType as keyof typeof DataDateType],
        FromDate: fromDate,
        ToDate: toDate,
      });
    }
  }

  applyAdvancedFilter() {
    this.modalService.hide();
    this.filterFunction();
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['SubSources', 'Projects', 'Cities', 'Countries'];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'IsWithTeam',
        'isGM',
        'userStatus',
        'SearchText',
        'ShouldShowAll',
        'ShouldShowPercentage',
        'path',
        'ToDate',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'FromDate' && values) {
      if (key === 'FromDate' && values !== null) {
        const toDate = new Date(this.filterDate[0]);
        const fromDate = new Date(this.filterDate[1]);
        const formattedToDate = moment(toDate).format('DD-MM-YYYY');
        const formattedFromDate = moment(fromDate).format('DD-MM-YYYY');
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    } else if (key === 'DateType') {
      return [ReportDateType[Number(values)]];
    }
    return values?.toString()?.split(',');
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (['DateType', 'date'].includes(key)) {
      delete this.filtersPayload[key];
      const dependentKey = key === 'date' ? 'DateType' : 'date';
      if (this.filtersPayload[dependentKey]) {
        delete this.filtersPayload[dependentKey];
      }
    } else {
      this.filtersPayload[key] = this.filtersPayload[key]?.filter(
        (item: any, index: number) => {
          const matchIndex = this.filtersPayload[key]?.indexOf(value);
          return index !== matchIndex;
        }
      );
    }
    this.filterFunction();
  }

  reset() {
    this.filtersPayload = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  countryChanged() {
    let country = this.advanceFilterForm?.value?.Countries;
    if (country.length) {
      this.cities = this.allCities.filter((city: any) =>
        country.includes(city?.country)
      );
    } else {
      this.cities = [...this.allCities];
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
