import { Action } from '@ngrx/store';
import { SourcePayload, SourceUpdatePayload } from 'src/app/core/interfaces/source.interface';

export enum SourceActionTypes {
  FETCH_SOURCES = '[Source] Fetch Sources',
  FETCH_SOURCES_SUCCESS = '[Source] Fetch Sources Success',
  FETCH_SOURCES_FAILURE = '[Source] Fetch Sources Failure',

  ADD_SOURCE = '[Source] Add Source',
  ADD_SOURCE_SUCCESS = '[Source] Add Source Success',
  ADD_SOURCE_FAILURE = '[Source] Add Source Failure',

  UPDATE_SOURCE = '[Source] Update Source',
  UPDATE_SOURCE_SUCCESS = '[Source] Update Source Success',
  UPDATE_SOURCE_FAILURE = '[Source] Update Source Failure',

  DELETE_SOURCE = '[Source] Delete Source',
  DELETE_SOURCE_SUCCESS = '[Source] Delete Source Success',
  DELETE_SOURCE_FAILURE = '[Source] Delete Source Failure',

  UPDATE_SOURCE_STATUS = '[Source] Update Source Status',
  UPDATE_SOURCE_STATUS_SUCCESS = '[Source] Update Source Status Success',
  UPDATE_SOURCE_STATUS_FAILURE = '[Source] Update Source Status Failure',

  BULK_UPDATE_SOURCE_STATUS = '[Source] Bulk Update Source Status',
  BULK_UPDATE_SOURCE_STATUS_SUCCESS = '[Source] Bulk Update Source Status Success',
  BULK_UPDATE_SOURCE_STATUS_FAILURE = '[Source] Bulk Update Source Status Failure',

  GET_LEAD_DATA_COUNT = '[Source] Get Lead Data Count',
  GET_LEAD_DATA_COUNT_SUCCESS = '[Source] Get Lead Data Count Success',
  GET_LEAD_DATA_COUNT_FAILURE = '[Source] Get Lead Data Count Failure',

  CONVERT_TO_DIRECT = '[Source] Convert To Direct',
  CONVERT_TO_DIRECT_SUCCESS = '[Source] Convert To Direct Success',
  CONVERT_TO_DIRECT_FAILURE = '[Source] Convert To Direct Failure',

  EXIST_SOURCE = '[Source] Exist Source',
  EXIST_SOURCE_SUCCESS = '[Source] Exist Source Success',

  // Integration Actions
  ENABLE_SOURCE_INTEGRATION = '[Source] Enable Source Integration',
  ENABLE_SOURCE_INTEGRATION_SUCCESS = '[Source] Enable Source Integration Success',

  // Account Management Actions
  ADD_SOURCE_ACCOUNT = '[Source] Add Source Account',
  ADD_SOURCE_ACCOUNT_SUCCESS = '[Source] Add Source Account Success',

  GET_SOURCE_ACCOUNTS = '[Source] Get Source Accounts',
  GET_SOURCE_ACCOUNTS_SUCCESS = '[Source] Get Source Accounts Success',

  UPDATE_SOURCE_ACCOUNT = '[Source] Update Source Account',
  UPDATE_SOURCE_ACCOUNT_SUCCESS = '[Source] Update Source Account Success',

  DELETE_SOURCE_ACCOUNT = '[Source] Delete Source Account',
  DELETE_SOURCE_ACCOUNT_SUCCESS = '[Source] Delete Source Account Success',

  // Sub-source validation
  VALIDATE_SUB_SOURCE = '[Source] Validate Sub Source',
  VALIDATE_SUB_SOURCE_SUCCESS = '[Source] Validate Sub Source Success',

  // Fetch Sources with Sub-sources
  FETCH_SOURCES_WITH_SUB_SOURCES = '[Source] Fetch Sources With Sub Sources',
  FETCH_SOURCES_WITH_SUB_SOURCES_SUCCESS = '[Source] Fetch Sources With Sub Sources Success',
  FETCH_SOURCES_WITH_SUB_SOURCES_FAILURE = '[Source] Fetch Sources With Sub Sources Failure',
}

// Fetch Sources
export class FetchSources implements Action {
  readonly type = SourceActionTypes.FETCH_SOURCES;
}

export class FetchSourcesSuccess implements Action {
  readonly type = SourceActionTypes.FETCH_SOURCES_SUCCESS;
  constructor(public payload: any[]) {}
}

export class FetchSourcesFailure implements Action {
  readonly type = SourceActionTypes.FETCH_SOURCES_FAILURE;
  constructor(public payload: any) {}
}

// Add Source
export class AddSource implements Action {
  readonly type = SourceActionTypes.ADD_SOURCE;
  constructor(public payload: SourcePayload) {}
}

export class AddSourceSuccess implements Action {
  readonly type = SourceActionTypes.ADD_SOURCE_SUCCESS;
  constructor(public payload: any) {}
}

export class AddSourceFailure implements Action {
  readonly type = SourceActionTypes.ADD_SOURCE_FAILURE;
  constructor(public payload: any) {}
}

// Update Source
export class UpdateSource implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE;
  constructor(public payload: SourceUpdatePayload) {}
}

export class UpdateSourceSuccess implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_SUCCESS;
  constructor(public payload: any) {}
}

export class UpdateSourceFailure implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_FAILURE;
  constructor(public payload: any) {}
}

// Delete Source
export class DeleteSource implements Action {
  readonly type = SourceActionTypes.DELETE_SOURCE;
  constructor(public payload: string) {}
}

export class DeleteSourceSuccess implements Action {
  readonly type = SourceActionTypes.DELETE_SOURCE_SUCCESS;
  constructor(public payload: any) {}
}

export class DeleteSourceFailure implements Action {
  readonly type = SourceActionTypes.DELETE_SOURCE_FAILURE;
  constructor(public payload: any) {}
}

// Update Source Status
export class UpdateSourceStatus implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_STATUS;
  constructor(public id: string, public isEnabled: boolean) {}
}

export class UpdateSourceStatusSuccess implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_STATUS_SUCCESS;
  constructor(public payload: any) {}
}

export class UpdateSourceStatusFailure implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_STATUS_FAILURE;
  constructor(public payload: any) {}
}

// Bulk Update Source Status
export class BulkUpdateSourceStatus implements Action {
  readonly type = SourceActionTypes.BULK_UPDATE_SOURCE_STATUS;
  constructor(public sourceIds: string[], public isEnabled: boolean) {}
}

export class BulkUpdateSourceStatusSuccess implements Action {
  readonly type = SourceActionTypes.BULK_UPDATE_SOURCE_STATUS_SUCCESS;
  constructor(public payload: any) {}
}

export class BulkUpdateSourceStatusFailure implements Action {
  readonly type = SourceActionTypes.BULK_UPDATE_SOURCE_STATUS_FAILURE;
  constructor(public payload: any) {}
}

// Get Lead and Data Count
export class GetLeadDataCount implements Action {
  readonly type = SourceActionTypes.GET_LEAD_DATA_COUNT;
  constructor(public sourceValue: string) {}
}

export class GetLeadDataCountSuccess implements Action {
  readonly type = SourceActionTypes.GET_LEAD_DATA_COUNT_SUCCESS;
  constructor(public payload: any) {}
}

export class GetLeadDataCountFailure implements Action {
  readonly type = SourceActionTypes.GET_LEAD_DATA_COUNT_FAILURE;
  constructor(public payload: any) {}
}

// Convert to Direct
export class ConvertToDirect implements Action {
  readonly type = SourceActionTypes.CONVERT_TO_DIRECT;
  constructor(public sourceValue: number) {}
}

export class ConvertToDirectSuccess implements Action {
  readonly type = SourceActionTypes.CONVERT_TO_DIRECT_SUCCESS;
  constructor(public payload: any) {}
}

export class ConvertToDirectFailure implements Action {
  readonly type = SourceActionTypes.CONVERT_TO_DIRECT_FAILURE;
  constructor(public payload: any) {}
}

export class ExistSource implements Action {
  readonly type = SourceActionTypes.EXIST_SOURCE;
  constructor(public sourceName: string) {}
}

export class ExistSourceSuccess implements Action {
  readonly type = SourceActionTypes.EXIST_SOURCE_SUCCESS;
  constructor(public response: boolean) {}
}

// Enable Source Integration
export class EnableSourceIntegration implements Action {
  readonly type = SourceActionTypes.ENABLE_SOURCE_INTEGRATION;
  constructor(public sourceId: string, public isEnabled: boolean) {}
}

export class EnableSourceIntegrationSuccess implements Action {
  readonly type = SourceActionTypes.ENABLE_SOURCE_INTEGRATION_SUCCESS;
  constructor(public payload: any) {}
}

// Add Source Account
export class AddSourceAccount implements Action {
  readonly type = SourceActionTypes.ADD_SOURCE_ACCOUNT;
  constructor(public payload: any) {}
}

export class AddSourceAccountSuccess implements Action {
  readonly type = SourceActionTypes.ADD_SOURCE_ACCOUNT_SUCCESS;
  constructor(public payload: any) {}
}

// Get Source Accounts
export class GetSourceAccounts implements Action {
  readonly type = SourceActionTypes.GET_SOURCE_ACCOUNTS;
  constructor(public sourceId: string) {}
}

export class GetSourceAccountsSuccess implements Action {
  readonly type = SourceActionTypes.GET_SOURCE_ACCOUNTS_SUCCESS;
  constructor(public payload: any[]) {}
}

// Update Source Account
export class UpdateSourceAccount implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_ACCOUNT;
  constructor(public accountId: string, public payload: any) {}
}

export class UpdateSourceAccountSuccess implements Action {
  readonly type = SourceActionTypes.UPDATE_SOURCE_ACCOUNT_SUCCESS;
  constructor(public payload: any) {}
}

// Delete Source Account
export class DeleteSourceAccount implements Action {
  readonly type = SourceActionTypes.DELETE_SOURCE_ACCOUNT;
  constructor(public accountId: string) {}
}

export class DeleteSourceAccountSuccess implements Action {
  readonly type = SourceActionTypes.DELETE_SOURCE_ACCOUNT_SUCCESS;
  constructor(public payload: any) {}
}

// Validate Sub Source
export class ValidateSubSource implements Action {
  readonly type = SourceActionTypes.VALIDATE_SUB_SOURCE;
  constructor(public subSourceName: string, public excludeSourceId?: string) {}
}

export class ValidateSubSourceSuccess implements Action {
  readonly type = SourceActionTypes.VALIDATE_SUB_SOURCE_SUCCESS;
  constructor(public isValid: boolean) {}
}

// Fetch Sources with Sub-sources
export class FetchSourcesWithSubSources implements Action {
  readonly type = SourceActionTypes.FETCH_SOURCES_WITH_SUB_SOURCES;
}

export class FetchSourcesWithSubSourcesSuccess implements Action {
  readonly type = SourceActionTypes.FETCH_SOURCES_WITH_SUB_SOURCES_SUCCESS;
  constructor(public payload: any[]) {}
}

export class FetchSourcesWithSubSourcesFailure implements Action {
  readonly type = SourceActionTypes.FETCH_SOURCES_WITH_SUB_SOURCES_FAILURE;
  constructor(public payload: any) {}
}

export type SourceActions =
  | FetchSources
  | FetchSourcesSuccess
  | FetchSourcesFailure
  | AddSource
  | AddSourceSuccess
  | AddSourceFailure
  | UpdateSource
  | UpdateSourceSuccess
  | UpdateSourceFailure
  | DeleteSource
  | DeleteSourceSuccess
  | DeleteSourceFailure
  | UpdateSourceStatus
  | UpdateSourceStatusSuccess
  | UpdateSourceStatusFailure
  | BulkUpdateSourceStatus
  | BulkUpdateSourceStatusSuccess
  | BulkUpdateSourceStatusFailure
  | GetLeadDataCount
  | GetLeadDataCountSuccess
  | GetLeadDataCountFailure
  | ConvertToDirect
  | ConvertToDirectSuccess
  | ConvertToDirectFailure
  | ExistSource
  | ExistSourceSuccess
  | EnableSourceIntegration
  | EnableSourceIntegrationSuccess
  | AddSourceAccount
  | AddSourceAccountSuccess
  | GetSourceAccounts
  | GetSourceAccountsSuccess
  | UpdateSourceAccount
  | UpdateSourceAccountSuccess
  | DeleteSourceAccount
  | DeleteSourceAccountSuccess
  | ValidateSubSource
  | ValidateSubSourceSuccess
  | FetchSourcesWithSubSources
  | FetchSourcesWithSubSourcesSuccess
  | FetchSourcesWithSubSourcesFailure;
