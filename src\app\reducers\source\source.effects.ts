import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';

import { SourceService } from 'src/app/services/controllers/source.service';
import {
  AddSource,
  AddSourceFailure,
  AddSourceSuccess,
  BulkUpdateSourceStatus,
  BulkUpdateSourceStatusFailure,
  BulkUpdateSourceStatusSuccess,
  ConvertToDirect,
  ConvertToDirectFailure,
  ConvertToDirectSuccess,
  DeleteSource,
  DeleteSourceFailure,
  DeleteSourceSuccess,
  ExistSource,
  ExistSourceSuccess,
  FetchSources,
  FetchSourcesFailure,
  FetchSourcesSuccess,
  FetchSourcesWithSubSourcesFailure,
  FetchSourcesWithSubSourcesSuccess,
  GetLeadDataCount,
  GetLeadDataCountFailure,
  GetLeadDataCountSuccess,
  SourceActionTypes,
  UpdateSource,
  UpdateSourceFailure,
  UpdateSourceStatus,
  UpdateSourceStatusFailure,
  UpdateSourceStatusSuccess,
  UpdateSourceSuccess,
  EnableSourceIntegration,
  EnableSourceIntegrationSuccess,
  AddSourceAccount,
  AddSourceAccountSuccess,
  GetSourceAccounts,
  GetSourceAccountsSuccess,
  UpdateSourceAccount,
  UpdateSourceAccountSuccess,
  DeleteSourceAccount,
  DeleteSourceAccountSuccess,
  ValidateSubSource,
  ValidateSubSourceSuccess,
} from './source.actions';

import { ManageSourceIndexDBService } from 'src/app/services/shared/managesource.indexdb.service';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class SourceEffects {
  constructor(
    private actions$: Actions,
    private sourceService: SourceService,
    private notificationService: NotificationsService,
    private manageSourceIndexDBService: ManageSourceIndexDBService,
    private commonService: CommonService
  ) { }

  fetchSources$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.FETCH_SOURCES),
      switchMap(() => {
        return this.manageSourceIndexDBService.getSourcesWithCaching().pipe(
          map((sources: any[]) => {
            console.log('Sources from IndexedDB:', sources);
            return new FetchSourcesSuccess(sources || []);
          }),
          catchError((error) => {
            console.error('Error fetching sources from IndexedDB:', error);
            return of(new FetchSourcesFailure(error));
          })
        );
      })
    )
  );

  addSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.ADD_SOURCE),
      map((action: AddSource) => action.payload),
      switchMap((payload) => {
        return this.sourceService.addSource(payload).pipe(
          map((response: any) => {
            if (response && response.succeeded) {
              this.notificationService.success('Source added successfully.');
              return new AddSourceSuccess(response.data);
            }
            this.notificationService.error('Failed to add source. Please try again.');
            return new AddSourceFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to add source. Please try again.');
            return of(new AddSourceFailure(error));
          })
        );
      })
    )
  );

  // After Add Source Success, refresh IndexedDB and fetch sources again
  addSourceSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.ADD_SOURCE_SUCCESS),
      tap(() => {
        // Clear IndexedDB cache to force refresh from API
        this.manageSourceIndexDBService.clearCaches();
      }),
      map(() => new FetchSources())
    )
  );

  updateSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.UPDATE_SOURCE),
      map((action: UpdateSource) => action.payload),
      switchMap((payload) => {
        return this.sourceService.updateSource(payload).pipe(
          map((response: any) => {
            if (response && response.succeeded) {
              this.notificationService.success('Source updated successfully.');
              return new UpdateSourceSuccess(response.data);
            }
            this.notificationService.error('Failed to update source. Please try again.');
            return new UpdateSourceFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to update source. Please try again.');
            return of(new UpdateSourceFailure(error));
          })
        );
      })
    )
  );

  // After Update Source Success, refresh IndexedDB and fetch sources again
  updateSourceSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.UPDATE_SOURCE_SUCCESS),
      tap(() => {
        // Clear IndexedDB cache to force refresh from API
        this.manageSourceIndexDBService.clearCaches();
      }),
      map(() => new FetchSources())
    )
  );

  deleteSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.DELETE_SOURCE),
      map((action: DeleteSource) => action.payload),
      switchMap((id) => {
        return this.sourceService.deleteSource(id).pipe(
          map((response) => {
            if (response && response.succeeded) {
              this.notificationService.success('Source deleted successfully.');
              return new DeleteSourceSuccess(response.data);
            }
            this.notificationService.error('Failed to delete source. Please try again.');
            return new DeleteSourceFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to delete source. Please try again.');
            return of(new DeleteSourceFailure(error));
          })
        );
      })
    )
  );

  // After Delete Source Success, refresh IndexedDB and fetch sources again
  deleteSourceSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.DELETE_SOURCE_SUCCESS),
      tap(() => {
        // Clear IndexedDB cache to force refresh from API
        this.manageSourceIndexDBService.clearCaches();
      }),
      map(() => new FetchSources())
    )
  );

  updateSourceStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.UPDATE_SOURCE_STATUS),
      map((action: UpdateSourceStatus) => ({ id: action.id, isEnabled: action.isEnabled })),
      switchMap(({ id, isEnabled }) => {
        return this.sourceService.updateSourceStatus(id, isEnabled).pipe(
          map((response) => {
            if (response && response.succeeded) {
              const message = isEnabled ? 'Source enabled successfully.' : 'Source disabled successfully.';
              this.notificationService.success(message);
              return new UpdateSourceStatusSuccess(response.data);
            }
            this.notificationService.error('Failed to update source status. Please try again.');
            return new UpdateSourceStatusFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to update source status. Please try again.');
            return of(new UpdateSourceStatusFailure(error));
          })
        );
      })
    )
  );

  // After Update Source Status Success, refresh IndexedDB and fetch sources again
  updateSourceStatusSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.UPDATE_SOURCE_STATUS_SUCCESS),
      tap(() => {
        // Clear IndexedDB cache to force refresh from API
        this.manageSourceIndexDBService.clearCaches();
      }),
      map(() => new FetchSources())
    )
  );

  bulkUpdateSourceStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.BULK_UPDATE_SOURCE_STATUS),
      map((action: BulkUpdateSourceStatus) => ({ sourceIds: action.sourceIds, isEnabled: action.isEnabled })),
      switchMap(({ sourceIds, isEnabled }) => {
        return this.sourceService.bulkUpdateSourceStatus(sourceIds, isEnabled).pipe(
          map((response) => {
            if (response && response.succeeded) {
              const message = isEnabled ? 'Source(s) enabled successfully.' : 'Source(s) disabled successfully.';
              this.notificationService.success(message);
              return new BulkUpdateSourceStatusSuccess(response.data);
            }
            this.notificationService.error('Failed to update source status. Please try again.');
            return new BulkUpdateSourceStatusFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to update source status. Please try again.');
            return of(new BulkUpdateSourceStatusFailure(error));
          })
        );
      })
    )
  );

  // After Bulk Update Source Status Success, refresh IndexedDB and fetch sources again
  bulkUpdateSourceStatusSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.BULK_UPDATE_SOURCE_STATUS_SUCCESS),
      tap(() => {
        // Clear IndexedDB cache to force refresh from API
        this.manageSourceIndexDBService.clearCaches();
      }),
      map(() => new FetchSources())
    )
  );

  getLeadDataCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.GET_LEAD_DATA_COUNT),
      map((action: GetLeadDataCount) => action.sourceValue),
      switchMap((sourceValue) => {
        return this.sourceService.getLeadAndDataCount(sourceValue).pipe(
          map((response) => {
            if (response && response.succeeded) {
              return new GetLeadDataCountSuccess(response.data);
            }
            return new GetLeadDataCountFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to check source usage. Please try again.');
            return of(new GetLeadDataCountFailure(error));
          })
        );
      })
    )
  );

  convertToDirect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.CONVERT_TO_DIRECT),
      map((action: ConvertToDirect) => action.sourceValue),
      switchMap((sourceValue) => {
        return this.sourceService.convertToDirect(sourceValue).pipe(
          map((response) => {
            if (response && response.succeeded) {
              this.notificationService.success('Source converted to direct successfully.');
              return new ConvertToDirectSuccess(response.data);
            }
            this.notificationService.error('Failed to convert source to direct. Please try again.');
            return new ConvertToDirectFailure(response);
          }),
          catchError((error) => {
            this.notificationService.error('Failed to convert source to direct. Please try again.');
            return of(new ConvertToDirectFailure(error));
          })
        );
      })
    )
  );

  existSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.EXIST_SOURCE),
      map((action: ExistSource) => action.sourceName),
      switchMap((sourceName) => {
        return this.sourceService.existSource(sourceName).pipe(
          map((response) => {
            if (response && response.succeeded) {
              return new ExistSourceSuccess(response.data);
            }
            return new ExistSourceSuccess(false);
          }),
          catchError((error) => {
            return of(new ExistSourceSuccess(false));
          })
        );
      })
    )
  );


  // After Convert to Direct Success, refresh IndexedDB and fetch sources again
  convertToDirectSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.CONVERT_TO_DIRECT_SUCCESS),
      tap(() => {
        // Clear IndexedDB cache to force refresh from API
        this.manageSourceIndexDBService.clearCaches();
      }),
      map(() => new FetchSources())
    )
  );

  enableSourceIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.ENABLE_SOURCE_INTEGRATION),
      map((action: EnableSourceIntegration) => ({ sourceId: action.sourceId, isEnabled: action.isEnabled })),
      switchMap(({ sourceId, isEnabled }) => {
        return this.sourceService.enableSourceIntegration(sourceId, isEnabled).pipe(
          map((response) => {
            if (response && response.succeeded) {
              const message = isEnabled ? 'Source integration enabled successfully.' : 'Source integration disabled successfully.';
              this.notificationService.success(message);
              return new EnableSourceIntegrationSuccess(response.data);
            }
            this.notificationService.error('Failed to update source integration. Please try again.');
            return new EnableSourceIntegrationSuccess(null);
          }),
          catchError((err) => {
            this.notificationService.error('Failed to update source integration. Please try again.');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  addSourceAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.ADD_SOURCE_ACCOUNT),
      map((action: AddSourceAccount) => action.payload),
      switchMap((payload) => {
        return this.sourceService.addSourceAccount(payload).pipe(
          map((response) => {
            if (response && response.succeeded) {
              this.notificationService.success('Account added successfully.');
              return new AddSourceAccountSuccess(response.data);
            }
            this.notificationService.error('Failed to add account. Please try again.');
            return new AddSourceAccountSuccess(null);
          }),
          catchError((err) => {
            this.notificationService.error('Failed to add account. Please try again.');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getSourceAccounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.GET_SOURCE_ACCOUNTS),
      map((action: GetSourceAccounts) => action.sourceId),
      switchMap((sourceId) => {
        return this.sourceService.getSourceAccounts(sourceId).pipe(
          map((response) => {
            if (response && response.succeeded) {
              return new GetSourceAccountsSuccess(response.data);
            }
            return new GetSourceAccountsSuccess([]);
          }),
          catchError((err) => {
            this.notificationService.error('Failed to fetch accounts. Please try again.');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateSourceAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.UPDATE_SOURCE_ACCOUNT),
      map((action: UpdateSourceAccount) => ({ accountId: action.accountId, payload: action.payload })),
      switchMap(({ accountId, payload }) => {
        return this.sourceService.updateSourceAccount(accountId, payload).pipe(
          map((response) => {
            if (response && response.succeeded) {
              this.notificationService.success('Account updated successfully.');
              return new UpdateSourceAccountSuccess(response.data);
            }
            this.notificationService.error('Failed to update account. Please try again.');
            return new UpdateSourceAccountSuccess(null);
          }),
          catchError((err) => {
            this.notificationService.error('Failed to update account. Please try again.');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  deleteSourceAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.DELETE_SOURCE_ACCOUNT),
      map((action: DeleteSourceAccount) => action.accountId),
      switchMap((accountId) => {
        return this.sourceService.deleteSourceAccount(accountId).pipe(
          map((response) => {
            if (response && response.succeeded) {
              this.notificationService.success('Account deleted successfully.');
              return new DeleteSourceAccountSuccess(response.data);
            }
            this.notificationService.error('Failed to delete account. Please try again.');
            return new DeleteSourceAccountSuccess(null);
          }),
          catchError((err) => {
            this.notificationService.error('Failed to delete account. Please try again.');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  validateSubSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.VALIDATE_SUB_SOURCE),
      map((action: ValidateSubSource) => ({ subSourceName: action.subSourceName, excludeSourceId: action.excludeSourceId })),
      switchMap(({ subSourceName, excludeSourceId }) => {
        return this.sourceService.validateSubSource(subSourceName, excludeSourceId).pipe(
          map((response) => {
            if (response && response.succeeded) {
              return new ValidateSubSourceSuccess(response.data);
            }
            return new ValidateSubSourceSuccess(false);
          }),
          catchError((err) => {
            return of(new OnError(err));
          })
        );
      })
    )
  );

  fetchSourcesWithSubSources$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SourceActionTypes.FETCH_SOURCES_WITH_SUB_SOURCES),
      switchMap(() => {
        return this.sourceService.getAllSourcesWithSubSources().pipe(
          map((response: any) => {
            if (response && response.succeeded) {
              const mappedData = (response.data || []).map((item: any) => ({
                ...item,
                displayName: item.displayName || item.leadSource || item.name,
                name: item.name || item.leadSource,
                subSources: item.subSources || []
              }));
              return new FetchSourcesWithSubSourcesSuccess(mappedData);
            }
            return new FetchSourcesWithSubSourcesSuccess([]);
          }),
          catchError((error) => {
            console.error('Error fetching sources with sub-sources:', error);
            return of(new FetchSourcesWithSubSourcesFailure(error));
          })
        );
      })
    )
  );
}
