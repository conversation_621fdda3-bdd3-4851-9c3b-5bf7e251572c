import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
import { SourcePayload, SourceUpdatePayload, SourceAccountPayload, ExtractedFields } from 'src/app/core/interfaces/source.interface';

@Injectable({
  providedIn: 'root',
})
export class SourceService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'source';
  }

  addSource(payload: SourcePayload): Observable<any> {
    return this.http.post(`${this.serviceBaseUrl}`, payload);
  }
  
  updateSource(payload: SourceUpdatePayload): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}`, payload);
  }

  updateSourceStatus(id: string, isEnabled: boolean): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/status/${id}`, { isEnabled });
  }

  bulkUpdateSourceStatus(sourceIds: string[], isEnabled: boolean): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/status/multiple`, { sourceIds, isEnabled });
  }

  getLeadAndDataCount(sourceValue: string): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/leadanddata-count?sourceValue=${sourceValue}`);
  }

  convertToDirect(sourceValue: number): Observable<any> {
    return this.http.put(`${env.baseURL}api/v1/source/converttodirect`, { "sourceValue": sourceValue });
  }

  deleteSource(id: string): Observable<any> {
    return this.http.delete(`${this.serviceBaseUrl}/${id}`);
  }

  existSource(sourceName: string): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/exist?displayname=${sourceName}`);
  }

  // Integration Methods
  enableSourceIntegration(sourceId: string, isEnabled: boolean): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/integration/${sourceId}`, { isEnabled });
  }

  // Account Management Methods
  addSourceAccount(payload: SourceAccountPayload): Observable<any> {
    return this.http.post(`${this.serviceBaseUrl}/accounts`, payload);
  }

  getSourceAccounts(sourceId: string): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/accounts?sourceId=${sourceId}`);
  }

  updateSourceAccount(accountId: string, payload: SourceAccountPayload): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/accounts/${accountId}`, payload);
  }

  deleteSourceAccount(accountId: string): Observable<any> {
    return this.http.delete(`${this.serviceBaseUrl}/accounts/${accountId}`);
  }

  // cURL Processing Methods
  extractFieldsFromCurl(curlCommand: string): Observable<ExtractedFields> {
    return this.http.post<ExtractedFields>(`${this.serviceBaseUrl}/extract-fields`, { curlCommand });
  }

  testWebhookConnection(webhookUrl: string, payload: any): Observable<any> {
    return this.http.post(`${this.serviceBaseUrl}/test-webhook`, { webhookUrl, payload });
  }

  // Sub-source Validation
  validateSubSource(subSourceName: string, excludeSourceId?: string): Observable<any> {
    let url = `${this.serviceBaseUrl}/validate-subsource?subSourceName=${subSourceName}`;
    if (excludeSourceId) {
      url += `&excludeSourceId=${excludeSourceId}`;
    }
    return this.http.get(url);
  }

  // Get Default Payload Mappings
  getDefaultPayloadMappings(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/default-payload-mappings`);
  }

  // Get Available Fields for Mapping
  getAvailableFields(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/available-fields`);
  }

  // Get All Sources with Sub-sources
  getAllSourcesWithSubSources(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/withsubsources`);
  }
}
