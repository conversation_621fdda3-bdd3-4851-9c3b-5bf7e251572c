import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { takeUntil } from 'rxjs';


import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getBHKDisplayString,
  getLocationDetailsByObj,
  getTimeZoneDate,
  isEmptyObject,
} from 'src/app/core/utils/common.util';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { EMPTY_GUID, UPDATE_STATUS } from 'src/app/app.constants';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';

@Component({
  selector: 'lead-information',
  templateUrl: './lead-information.component.html',
})
export class LeadInformationComponent implements OnInit, OnChanges, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() manualLocationsList: any;
  @Input() leadInfo: any;
  @Input() liveFormValues: any;
  @Input() canViewLeadSource: boolean;
  leadSource: string;
  formValues: any;
  getAssignedToDetails = getAssignedToDetails;
  getBHKDisplayString = getBHKDisplayString;
  getTimeZoneDate = getTimeZoneDate;
  moment = moment;
  allUsers: any[] = [];
  showScheduledDate: boolean = false;
  placesList: any;
  isDualOwnershipEnabled: boolean = false;
  userData: any;
  get cities() {
    return (
      [
        ...(this.manualLocationsList?.map((address: any) =>
          getLocationDetailsByObj(address)
        ) || []),
        ...(this.formValues?.currentValue?.locationId?.map?.(
          (location: any) => location?.location
        ) || []),
        ...(this.formValues?.currentValue?.enquiredCity?.trim() ||
          this.formValues?.currentValue?.enquiredState?.trim() ||
          this.formValues?.currentValue?.enquiredLocality?.trim()
          ? [
            getLocationDetailsByObj({
              subLocality: this.formValues?.currentValue?.enquiredLocality,
              city: this.formValues?.currentValue?.enquiredCity,
              state: this.formValues?.currentValue?.enquiredState,
            }),
          ]
          : []),
      ].join(': ') || '--'
    );
  }

  get enquiredFor() {
    return this.formValues?.currentValue?.enquiryTypes?.join(', ') || '--';
  }

  get bhkNo() {
    return (
      this.formValues?.currentValue?.bhkNo
        ?.map((number: string) => getBHKDisplayString(number))
        ?.join(', ') || '--'
    );
  }

  get bhkTypes() {
    return this.formValues?.currentValue?.bhkTypes?.join(', ') || '--';
  }

  constructor(private _store: Store<AppState>, private router: Router) {
    if (
      this.router.url.includes('add-lead') ||
      this.router.url.includes('edit-lead')
    ) {
      this._store.dispatch(new FetchUsersListForReassignment());
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.liveFormValues) this.formValues = changes.liveFormValues;
    this.ngOnInit();
  }
  ngOnInit() {
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = data;
      });

    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data?.items;
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;
      });
    if (
      this.router.url.includes('add-lead') ||
      this.router.url.includes('edit-lead')
    ) {
      let assignToUserName = getAssignedToDetails(
        this.formValues?.currentValue?.assignTo || '',
        this.allUsers
      );
      let secondaryAssignToUserName = getAssignedToDetails(
        this.formValues?.currentValue?.secondaryAssignTo || '',
        this.allUsers
      );

      const locality = this.formValues?.currentValue?.enquiredLocality || '';
      const city = this.formValues?.currentValue?.enquiredCity || '';
      const state = this.formValues?.currentValue?.enquiredState || '';
      let location = '';

      if (locality) {
        location += locality + ', ';
      }
      if (city) {
        location += city + ', ';
      }
      if (state) {
        location += state;
      }
      location = location.replace(/,\s*$/, '');

      this.leadInfo = {
        ...this.leadInfo,
        name: this.formValues?.currentValue?.name,
        contactNo: this.formValues?.currentValue?.contactNo,
        alternateContactNo: this.formValues?.currentValue?.alternateContactNo,
        email: this.formValues?.currentValue?.email,
        assignTo: !isEmptyObject(assignToUserName || {})
          ? `${assignToUserName?.firstName} ${assignToUserName?.lastName}`
          : '',
        secondaryUserId: !isEmptyObject(secondaryAssignToUserName || {})
          ? `${secondaryAssignToUserName?.firstName} ${secondaryAssignToUserName?.lastName}`
          : '',
        referralName: this.formValues?.currentValue?.referralName,
        referralContactNo: this.formValues?.currentValue?.referralContactNo,
        leadSource: this.formValues?.currentValue?.leadSource,
        subSource: this.formValues?.currentValue?.subSource,
        city:
          location ||
          this.formValues?.currentValue?.locationId?.location ||
          getLocationDetailsByObj(this.leadInfo?.enquiry?.address),
        propertyType: this.formValues?.currentValue?.propertyTypeId,
        propertySubType: this.formValues?.currentValue?.propSubType,
        noOfBHK: this.formValues?.currentValue?.noOfBHK,
        bhkType:
          this.formValues?.currentValue?.bhkType !== 'None'
            ? this.formValues?.currentValue?.bhkType
            : null,
        lowerBudget: this.formValues?.currentValue?.lowerBudget,
        upperBudget: this.formValues?.currentValue?.upperBudget,
        enquiredFor:
          this.formValues?.currentValue?.enquiredFor !== 'None'
            ? this.formValues?.currentValue?.enquiredFor
            : null,
        project: this.formValues?.currentValue?.projectsList?.length
          ? this.formValues?.currentValue?.projectsList
          : null,
        property: this.formValues?.currentValue?.propertiesList?.length
          ? this.formValues?.currentValue?.propertiesList
          : null,
        agencies: this.formValues?.currentValue?.agencies?.length
          ? this.formValues?.currentValue?.agencies
          : null,
        notes: this.formValues?.currentValue?.notes,
        createdBy:
          this.leadInfo?.createdBy == EMPTY_GUID
            ? ''
            : this.leadInfo?.createdBy,
        createdOn: this.leadInfo?.createdOn,
        lastModifiedBy:
          this.leadInfo?.lastModifiedBy == EMPTY_GUID
            ? ''
            : this.leadInfo?.lastModifiedBy,
        lastModifiedOn: this.leadInfo?.lastModifiedOn,
      };
    }
    if (
      this.leadInfo.status?.actionName === UPDATE_STATUS.callback ||
      this.leadInfo.status?.actionName === UPDATE_STATUS['schedule-meeting'] ||
      this.leadInfo.status?.actionName === UPDATE_STATUS['schedule-site-visit']
    ) {
      this.showScheduledDate = true;
    }
  }

  getAgencyNames(): string {
    if (
      this.leadInfo &&
      this.leadInfo.agencies &&
      this.leadInfo.agencies.length
    ) {
      return this.leadInfo.agencies
        .map((agency: any) => agency.name || agency)
        .join(',');
    } else {
      return '--';
    }
  }

  getCampaign(): string {
    if (this.leadInfo && this.leadInfo.campaigns && this.leadInfo.campaigns.length) {
      return this.leadInfo.campaigns.map((campaigns: any) => campaigns.name || campaigns).join(',');
    } else {
      return '--';
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
